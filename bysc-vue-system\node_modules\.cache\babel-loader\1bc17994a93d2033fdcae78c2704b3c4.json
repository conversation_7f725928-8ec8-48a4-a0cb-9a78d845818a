{"remainingRequest": "D:\\bw\\idcardbox-vue\\bysc-vue-system\\node_modules\\babel-loader\\lib\\index.js!D:\\bw\\idcardbox-vue\\bysc-vue-system\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\bw\\idcardbox-vue\\bysc-vue-system\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\bw\\idcardbox-vue\\bysc-vue-system\\src\\bysc_system\\views\\role\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\bw\\idcardbox-vue\\bysc-vue-system\\src\\bysc_system\\views\\role\\index.vue", "mtime": 1753952638725}, {"path": "D:\\bw\\idcardbox-vue\\bysc-vue-system\\babel.config.js", "mtime": 1745205562429}, {"path": "D:\\bw\\idcardbox-vue\\bysc-vue-system\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745221302709}, {"path": "D:\\bw\\idcardbox-vue\\bysc-vue-system\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1745221307620}, {"path": "D:\\bw\\idcardbox-vue\\bysc-vue-system\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745221302709}, {"path": "D:\\bw\\idcardbox-vue\\bysc-vue-system\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1745221307761}], "contextDependencies": [], "result": ["import \"core-js/modules/es7.object.get-own-property-descriptors\";\nimport \"core-js/modules/es6.function.name\";\nimport \"core-js/modules/es6.number.constructor\";\nimport \"core-js/modules/es6.array.find-index\";\nimport \"core-js/modules/es6.string.starts-with\";\nimport \"core-js/modules/es6.object.keys\";\nimport \"core-js/modules/web.dom.iterable\";\nimport _defineProperty from \"D:/bw/idcardbox-vue/bysc-vue-system/node_modules/@babel/runtime/helpers/esm/defineProperty.js\";\nfunction ownKeys(e, r) { var t = Object.keys(e); if (Object.getOwnPropertySymbols) { var o = Object.getOwnPropertySymbols(e); r && (o = o.filter(function (r) { return Object.getOwnPropertyDescriptor(e, r).enumerable; })), t.push.apply(t, o); } return t; }\nfunction _objectSpread(e) { for (var r = 1; r < arguments.length; r++) { var t = null != arguments[r] ? arguments[r] : {}; r % 2 ? ownKeys(Object(t), !0).forEach(function (r) { _defineProperty(e, r, t[r]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) { Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r)); }); } return e; }\nimport Vue from \"vue\";\nimport { mapActions } from \"vuex\";\nimport Grid from \"@/components/Grid\";\nimport _ from \"lodash\";\nimport commonTree from \"@/components/treeComp/commonTree\";\nimport checkTree from \"@/components/treeComp/checkTree\";\nimport iconChoose from \"@/components/choose/icon-choose\";\nimport roleUser from \"@/bysc_system/views/role/roleUser\";\nimport Lazytrees from \"@/components/treeComp/ltrees.vue\";\nimport dayjs from \"dayjs\";\nvar defaultSearchForm = {\n  name: \"\",\n  code: \"\"\n};\nvar defaultForm = {\n  comments: \"\",\n  roleKey: \"\",\n  roleName: \"\",\n  roleType: \"\",\n  roleStatus: 1\n};\nexport default {\n  components: {\n    commonTree: commonTree,\n    Grid: Grid,\n    iconChoose: iconChoose,\n    checkTree: checkTree,\n    roleUser: roleUser,\n    Lazytrees: Lazytrees\n  },\n  destroyed: function destroyed() {\n    this.searchEventBus.$off();\n  },\n  data: function data() {\n    this.searchEventBus = new Vue();\n    return {\n      selectModuleName: [],\n      moduleNameLists: [],\n      loading: false,\n      selectedAppList: [],\n      appList: [{\n        name: \"工作流\",\n        id: 1\n      }, {\n        name: \"系统\",\n        id: 2\n      }, {\n        name: \"消息模块\",\n        id: 3\n      }, {\n        name: \"库存\",\n        id: 4\n      }, {\n        name: \"采购\",\n        id: 5\n      }, {\n        name: \"质量\",\n        id: 6\n      }, {\n        name: \"运行\",\n        id: 7\n      }],\n      bgurls: [{\n        url: require(\"./images/bg1.png\")\n      }, {\n        url: require(\"./images/bg2.png\")\n      }, {\n        url: require(\"./images/bg3.png\")\n      }],\n      defaultUserProps: {\n        children: \"children\",\n        label: \"label\",\n        roles: []\n      },\n      selectedUserLists: [],\n      userLists: [],\n      dialogUserVisible: false,\n      filterText: \"\",\n      defaultProps: {\n        children: \"children\",\n        label: \"roleName\",\n        roles: []\n      },\n      selectedRoleLists: [],\n      roleLists: [],\n      dialogRoleVisible: false,\n      defaultDeptProps: {\n        children: \"children\",\n        label: \"organizationName\",\n        roles: []\n      },\n      selectedDeptLists: [],\n      DeptLists: [],\n      dialogDeptVisible: false,\n      dataForm: {\n        moduleNames: [],\n        permissionType: \"\",\n        permissionTypeCode: \"\",\n        permissionTypeCode1: \"\",\n        refIds: [],\n        roleId: null,\n        allUserDeptIds: []\n      },\n      appForm: {\n        appIds: [],\n        roleId: null\n      },\n      dataDrawer: false,\n      appDrawer: false,\n      dialogVisible: false,\n      roleStatus: true,\n      checkStrictly: true,\n      ruleForm: _.cloneDeep(defaultForm),\n      rules: {\n        roleName: [{\n          required: true,\n          message: \"请输入名称\",\n          trigger: \"blur\"\n        }, {\n          min: 1,\n          max: 15,\n          message: \"长度在 1 到 15 个字符\",\n          trigger: \"blur\"\n        }],\n        roleKey: [{\n          required: true,\n          message: \"请输入标识\",\n          trigger: \"blur\"\n        }],\n        roleType: [{\n          required: true,\n          message: \"请选择角色类型\",\n          trigger: \"change\"\n        }]\n      },\n      drawerName: \"添加\",\n      drawer: false,\n      menuDrawer: false,\n      direction: \"rtl\",\n      searchForm: _.cloneDeep(defaultSearchForm),\n      treeProps: {\n        children: \"children\",\n        label: \"resourceName\"\n      },\n      resourceName: \"\",\n      columns: [{\n        title: \"名称\",\n        key: \"roleName\",\n        tooltip: true,\n        minWidth: 130\n      }, {\n        title: \"标识\",\n        key: \"roleKey\",\n        minWidth: 150,\n        tooltip: true\n      }, {\n        title: \"角色状态\",\n        slot: \"roleStatus\",\n        tooltip: true,\n        minWidth: 170\n      }, {\n        title: \"角色类型\",\n        slot: \"roleType\",\n        tooltip: true,\n        minWidth: 170\n      }, {\n        title: \"角色描述\",\n        key: \"comments\",\n        tooltip: true,\n        minWidth: 170\n      }],\n      permissionsList: [],\n      roleName: \"\",\n      tableData: [],\n      selectedResouce: {},\n      treedata: [],\n      resourceIds: [],\n      roleForm: {\n        resourceIds: [],\n        roleId: \"\"\n      },\n      roleId: \"\",\n      roleDrawer: false,\n      appLists: []\n    };\n  },\n  watch: {\n    roleStatus: function roleStatus(val) {\n      this.ruleForm.roleStatus = val ? 1 : 0;\n    },\n    // 'dataForm.permissionTypeCode1'(val) {\n    //   if (val === 'SPECIFIC_USER') {\n    //     this.selectedUserLists = [];\n    //     this.selectedRoleLists = [];\n    //   } else if (val === 'SPECIFIC_DEPT') {\n    //     this.selectedRoleLists = [];\n    //     this.selectedUserLists = [];\n    //   } else if (val === 'SPECIFIC_ROLE') {\n    //     this.selectedDeptLists = [];\n    //     this.selectedUserLists = [];\n    //   }\n    // },\n    filterText: function filterText(val) {\n      this.$refs.tree.filter(val);\n    }\n  },\n  mounted: function mounted() {\n    this.getRoleLists();\n    this.getDeptTreeLists();\n    this.getUserLists();\n    this.getParamDatas();\n  },\n  methods: _objectSpread(_objectSpread({}, mapActions([\"download\"])), {}, {\n    goExport: function goExport() {\n      var _this = this;\n      this.loading = true;\n      this.$confirm(\"\\u662F\\u5426\\u786E\\u5B9A\\u5BFC\\u51FA\\uFF1F\", \"导出\", {\n        confirmButtonText: \"确定\",\n        cancelButtonText: \"取消\",\n        type: \"warning\"\n      }).then(function () {\n        var s = '';\n        Object.keys(_this.searchForm).forEach(function (key, i) {\n          if (_this.searchForm[key]) {\n            if (i != Object.keys(_this.searchForm).length - 1) {\n              s = s + key + '=' + _this.searchForm[key] + '&';\n            } else {\n              s = s + key + '=' + _this.searchForm[key];\n            }\n          }\n        });\n        _this.loading = false;\n        var api = '/api/system/role/download';\n        var url = \"\".concat(api, \"?\").concat(s);\n        var name = \"\\u89D2\\u8272\\u7BA1\\u7406-\".concat(dayjs(new Date()).format('YYYYMMDDHHmmss'), \".xlsx\");\n        var downData = {\n          url: url,\n          downLoad: name\n        };\n        _this.download(downData); // 下载\n      }).catch(function () {\n        _this.loading = false;\n      });\n    },\n    getParamDatas: function getParamDatas() {\n      var _this2 = this;\n      this.$api[\"sysDict/getParam\"]({\n        code: \"DATA_PERMISSION_MODULE_NAMES\"\n      }).then(function (data) {\n        _this2.moduleNameLists = data;\n      });\n    },\n    updateDeptRefIds: function updateDeptRefIds() {\n      var _this3 = this;\n      // 清空之前的 refIds\n      this.dataForm.refIds = [];\n\n      // 从当前选中的部门列表中重新构建 refIds\n      this.selectedDeptLists.forEach(function (item) {\n        _this3.dataForm.refIds.push(item.id);\n      });\n\n      // 记录修改后的 refIds\n    },\n    getUserData: function getUserData(e) {\n      var refIds = [];\n      e.forEach(function (r) {\n        refIds.push(r.primaryCode);\n      });\n      // this.dataForm.refIds = [];\n      this.dataForm.allUserDeptIds = refIds;\n      this.selectedUserLists = e;\n    },\n    submitData: function submitData() {\n      var _this4 = this;\n      this.loading = true;\n      if (this.dataForm.permissionType == 8) {\n        this.dataForm.permissionTypeCode = this.dataForm.permissionTypeCode1;\n\n        // 根据不同的权限类型，确保 refIds 是最新的\n        if (this.dataForm.permissionTypeCode1 === \"SPECIFIC_DEPT\") {\n          // 如果是指定部门，调用 updateDeptRefIds 确保 refIds 是最新的\n          this.updateDeptRefIds();\n        } else if (this.dataForm.permissionTypeCode1 === \"SPECIFIC_ROLE\") {\n          // 如果是指定角色，确保 refIds 是最新的\n          this.dataForm.refIds = [];\n          this.selectedRoleLists.forEach(function (item) {\n            _this4.dataForm.refIds.push(item.id);\n          });\n        } else if (this.dataForm.permissionTypeCode1 === \"SPECIFIC_USER\") {\n          // 如果是指定用户，确保 allUserDeptIds 是最新的\n          this.dataForm.refIds = [];\n          this.selectedUserLists.forEach(function (item) {\n            // 用户ID可能带有 \"user\" 前缀，需要去掉\n            var userId = item.primaryCode.startsWith(\"user\") ? item.primaryCode.substring(4) : item.primaryCode;\n            _this4.dataForm.refIds.push(userId);\n          });\n        }\n      } else {\n        this.dataForm.permissionTypeCode = this.dataForm.permissionType;\n      }\n\n      // 打印最终要提交的数据\n      this.$api[\"systems/datapermission\"](this.dataForm).then(function (data) {\n        _this4.$message.success(\"保存成功\");\n        _this4.dataDrawer = false;\n        _this4.loading = false;\n      }).catch(function (err) {\n        _this4.loading = false;\n        console.error(\"提交数据出错:\", err);\n      });\n    },\n    getAppLists: function getAppLists(id) {\n      var _this5 = this;\n      this.$api[\"systems/getAppLists\"]().then(function (data) {\n        _this5.appLists = data;\n        _this5.$api[\"systems/roleGrantedApps\"]({\n          roleId: id\n        }).then(function (data) {\n          _this5.appForm.appIds = data;\n        });\n      });\n    },\n    saveAppLists: function saveAppLists(id) {\n      var _this6 = this;\n      this.$api[\"systems/saveRoleApp\"](this.appForm).then(function (data) {\n        _this6.$message.success(\"应用配置成功\");\n        _this6.appDrawer = false;\n      });\n    },\n    getUserLists: function getUserLists() {\n      var _this7 = this;\n      this.$api[\"systems/org-user-tree\"]().then(function (data) {\n        _this7.userLists = data;\n      });\n    },\n    handleClose: function handleClose(tag) {\n      var index = this.selectedRoleLists.findIndex(function (e) {\n        return e.id === tag.id;\n      });\n      this.selectedRoleLists.splice(index, 1);\n      var ids = [];\n      this.selectedRoleLists.forEach(function (element) {\n        ids.push(element.id);\n      });\n\n      // 添加安全检查，确保 this.$refs.tree 存在\n      if (this.$refs.tree) {\n        this.$refs.tree.setCheckedKeys(ids);\n      } else {\n        console.warn(\"this.$refs.tree is undefined in handleClose\");\n      }\n\n      // 重新设置 dataForm.refIds，确保只包含当前选中的角色 ID\n      this.dataForm.refIds = [].concat(ids);\n    },\n    handleDeptClose: function handleDeptClose(tag) {\n      var index = this.selectedDeptLists.findIndex(function (e) {\n        return e.id === tag.id;\n      });\n      this.selectedDeptLists.splice(index, 1);\n      var ids = [];\n      this.selectedDeptLists.forEach(function (element) {\n        ids.push(element.id);\n      });\n\n      // 添加安全检查，确保 this.$refs.tree 存在\n      if (this.$refs.tree) {\n        this.$refs.tree.setCheckedKeys(ids);\n      } else {\n        console.warn(\"this.$refs.tree is undefined in handleDeptClose\");\n      }\n\n      // 调用 updateDeptRefIds 函数，同步更新 dataForm.refIds\n      this.updateDeptRefIds();\n    },\n    handleUserClose: function handleUserClose(tag) {\n      var index = this.selectedUserLists.findIndex(function (e) {\n        return e.primaryCode === tag.primaryCode;\n      });\n      this.selectedUserLists.splice(index, 1);\n      var ids = [];\n      this.selectedUserLists.forEach(function (element) {\n        ids.push(element.primaryCode);\n      });\n\n      // 添加安全检查，确保 this.$refs.ltree 存在\n      if (this.$refs.ltree) {\n        this.$refs.ltree.setCheckedKeys(ids);\n      } else {\n        console.warn(\"this.$refs.ltree is undefined in handleUserClose\");\n      }\n\n      // 重新设置 dataForm.allUserDeptIds，确保只包含当前选中的用户 ID\n      this.dataForm.allUserDeptIds = [].concat(ids);\n    },\n    getDeptTreeLists: function getDeptTreeLists() {\n      var _this8 = this;\n      this.$api[\"systems/organizationTree\"]({\n        parentId: 0\n      }).then(function (data) {\n        _this8.DeptLists = data;\n      });\n    },\n    getUserTreeLists: function getUserTreeLists() {\n      var _this9 = this;\n      this.$api[\"systems/organizationTree\"]({\n        parentId: 0\n      }).then(function (data) {\n        _this9.DeptLists = data;\n      });\n    },\n    getUserTrees: function getUserTrees(node, datas) {\n      this.dataForm.allUserDeptIds = datas.checkedKeys;\n      this.selectedUserLists = datas.checkedNodes;\n    },\n    getRoleTrees: function getRoleTrees(node, datas) {\n      this.dataForm.refIds = datas.checkedKeys;\n      this.selectedRoleLists = datas.checkedNodes;\n    },\n    getDeptTrees: function getDeptTrees(node, datas) {\n      // 先清空 selectedDeptLists\n      this.selectedDeptLists = datas.checkedNodes;\n      // 调用 updateDeptRefIds 函数，同步更新 dataForm.refIds\n      this.updateDeptRefIds();\n    },\n    filterNode: function filterNode(value, data) {\n      if (!value) {\n        return true;\n      }\n      return data.roleName.indexOf(value) !== -1;\n    },\n    filterDeptNode: function filterDeptNode(value, data) {\n      if (!value) {\n        return true;\n      }\n      return data.organizationName.indexOf(value) !== -1;\n    },\n    getRoleLists: function getRoleLists() {\n      var _this10 = this;\n      this.$api[\"systems/roleList\"]().then(function (data) {\n        _this10.roleLists = data;\n      });\n    },\n    addRoles: function addRoles() {\n      var _this11 = this;\n      this.dialogRoleVisible = true;\n      setTimeout(function () {\n        var ids = [];\n        _this11.selectedRoleLists.forEach(function (e) {\n          ids.push(e.id);\n        });\n        _this11.$refs.tree.setCheckedKeys(ids);\n      }, 100);\n    },\n    addUsers: function addUsers() {\n      var _this12 = this;\n      this.dialogUserVisible = true;\n      setTimeout(function () {\n        _this12.$refs.ltree.setTimer = new Date().getTime();\n        var ids = [];\n        _this12.selectedUserLists.forEach(function (e) {\n          ids.push(e.primaryCode);\n        });\n        _this12.$refs.ltree.setCheckedKeys(ids);\n        _this12.$refs.ltree.selectedNodes = _this12.selectedUserLists;\n        // console.log(this.$refs.ltree.selectedNode, \"设置\");\n      }, 100);\n    },\n    addDepts: function addDepts() {\n      var _this13 = this;\n      this.dialogDeptVisible = true;\n      setTimeout(function () {\n        var ids = [];\n        _this13.selectedDeptLists.forEach(function (e) {\n          ids.push(e.id);\n        });\n        _this13.$refs.tree.setCheckedKeys(ids);\n      }, 100);\n    },\n    getRoleUser: function getRoleUser(e) {\n      var _this14 = this;\n      this.roleDrawer = true;\n      setTimeout(function () {\n        _this14.$refs.roleUser.searchForm.roleId = e.id;\n        _this14.$refs.roleUser.searchTableData();\n      }, 50);\n    },\n    getSelectKeys: function getSelectKeys(e) {\n      var _this15 = this;\n      this.roleForm.resourceIds = [];\n      e && e.forEach(function (element) {\n        _this15.roleForm.resourceIds.push(element.id + \"\");\n      });\n    },\n    saveMenu: function saveMenu() {\n      var _this16 = this;\n      // roleresourcesave\n      if (!this.roleForm.resourceIds.length) {\n        this.$message.info(\"请选择对应资源在保存\");\n      } else {\n        this.$api[\"systems/roleresourcesave\"](this.roleForm).then(function (data) {\n          _this16.$message({\n            message: \"保存成功\",\n            type: \"success\"\n          });\n          _this16.menuDrawer = false;\n        });\n      }\n    },\n    getBindResourceIds: function getBindResourceIds() {\n      var _this17 = this;\n      this.$api[\"systems/getResourceIds\"]({\n        id: this.roleForm.roleId\n      }).then(function (data) {\n        _this17.resourceIds = [];\n        _this17.roleForm.resourceIds = [];\n        data.forEach(function (e) {\n          _this17.resourceIds.push(Number(e));\n          _this17.roleForm.resourceIds.push(Number(e));\n        });\n        _this17.checkStrictly = true;\n        _this17.$nextTick(function () {\n          _this17.$refs.ctree.setTree(_this17.resourceIds);\n          setTimeout(function () {\n            _this17.checkStrictly = false;\n          }, 500);\n        });\n      });\n    },\n    setDatas: function setDatas(e) {\n      var _this18 = this;\n      this.$api[\"systems/permissionquery\"]({\n        roleId: e.id\n      }).then(function (data) {\n        _this18.selectModuleName = [];\n        _this18.dataDrawer = true;\n        if (data) {\n          if (data.permissionTypeCode == \"SPECIFIC_DEPT\" || data.permissionTypeCode == \"SPECIFIC_USER\" || data.permissionTypeCode == \"SPECIFIC_ROLE\") {\n            _this18.dataForm.permissionType = 8;\n            _this18.dataForm.permissionTypeCode1 = data.permissionTypeCode;\n            _this18.dataForm.permissionTypeCode = data.permissionTypeCode;\n            _this18.dataForm.refIds = [];\n            _this18.selectedDeptLists = [];\n            _this18.selectedRoleLists = [];\n            _this18.selectedUserLists = [];\n            data.nameIdList.forEach(function (e) {\n              if (data.permissionTypeCode === \"SPECIFIC_USER\") {\n                _this18.selectedUserLists.push({\n                  primaryCode: \"user\" + e.id,\n                  label: e.name\n                });\n              } else if (data.permissionTypeCode === \"SPECIFIC_DEPT\") {\n                _this18.selectedDeptLists.push({\n                  organizationName: e.name,\n                  id: e.id\n                });\n              } else if (data.permissionTypeCode === \"SPECIFIC_ROLE\") {\n                _this18.selectedRoleLists.push({\n                  id: e.id,\n                  roleName: e.name\n                });\n              }\n              _this18.dataForm.refIds.push(e.id);\n            });\n          } else {\n            _this18.dataForm.permissionType = data.permissionTypeCode;\n            _this18.dataForm.permissionTypeCode = data.permissionTypeCode;\n            _this18.dataForm.refIds = [];\n          }\n          _this18.dataForm.moduleNames = data.moduleNames ? data.moduleNames : [];\n        } else {\n          _this18.selectedDeptLists = [];\n          _this18.selectedRoleLists = [];\n          _this18.selectedUserLists = [];\n          _this18.dataForm = {\n            moduleNames: [],\n            permissionType: \"\",\n            permissionTypeCode: \"\",\n            permissionTypeCode1: \"\",\n            refIds: [],\n            roleId: null\n          };\n        }\n        _this18.dataForm.roleId = e.id;\n      });\n    },\n    setApps: function setApps(e) {\n      this.appDrawer = true;\n      this.appForm.roleId = e.id;\n      this.getAppLists(e.id);\n    },\n    setMenu: function setMenu(e) {\n      this.menuDrawer = true;\n      this.roleForm.roleId = e.id;\n      this.getTrees();\n      this.getBindResourceIds();\n    },\n    handleAdd: function handleAdd() {\n      this.drawer = true;\n      this.drawerName = \"添加\";\n      this.ruleForm = _.cloneDeep(defaultForm);\n      this.roleStatus = true;\n    },\n    handleRdit: function handleRdit(data) {\n      this.ruleForm = JSON.parse(JSON.stringify(data));\n      this.roleStatus = !!data.roleStatus;\n      this.drawer = true;\n      this.drawerName = \"编辑\";\n    },\n    getParentName: function getParentName(arr, param) {\n      var _this19 = this;\n      var name = \"\";\n      arr.forEach(function (e) {\n        if (e.children) {\n          if (e.id == param) {\n            name = e.roleName;\n          }\n          _this19.getParentName(e.children, param);\n        } else {\n          if (e.id == param) {\n            name = e.roleName;\n          }\n        }\n      });\n      return name;\n    },\n    handleDelete: function handleDelete(e) {\n      var _this20 = this;\n      this.$confirm(\"您确定要删除该角色吗？\", \"提示\", {\n        confirmButtonText: \"确定\",\n        cancelButtonText: \"取消\",\n        type: \"warning\"\n      }).then(function () {\n        _this20.$api[\"systems/roleDelete\"]({\n          id: e\n        }).then(function (data) {\n          _this20.$refs.grid.query();\n          _this20.$message({\n            message: \"删除成功\",\n            type: \"success\"\n          });\n        });\n      }).catch(function () {});\n    },\n    submitForm: function submitForm(formName) {\n      var _this21 = this;\n      this.$refs[formName].validate(function (valid) {\n        if (valid) {\n          _this21.loading = true;\n          _this21.$api[\"systems/addRole\"](_this21.ruleForm).then(function (data) {\n            _this21.drawer = false;\n            _this21.loading = false;\n            _this21.$message({\n              type: \"success\",\n              message: \"保存成功\"\n            });\n            _this21.$refs.grid.query();\n          });\n        } else {\n          return false;\n        }\n      });\n    },\n    closeDrawer: function closeDrawer() {\n      this.ruleForm = _.cloneDeep(defaultForm);\n      this.$refs.ruleForm.resetFields();\n      this.drawer = false;\n    },\n    resetForm: function resetForm(formName) {\n      this.$refs[formName].resetFields();\n    },\n    searchTable: function searchTable() {\n      this.$refs.grid.query();\n    },\n    resetTable: function resetTable() {\n      var _this22 = this;\n      this.searchForm = _.cloneDeep(defaultSearchForm);\n      this.$nextTick(function () {\n        _this22.$refs.grid.query();\n      });\n    },\n    getcolumn: function getcolumn(e) {\n      this.columns = e;\n    },\n    getDatas: function getDatas(e) {\n      this.tableData = e;\n    },\n    gerTreeData: function gerTreeData() {\n      var _this23 = this;\n      if (this.resourceName) {\n        this.$api[\"systems/getTreeList\"]({\n          resourceName: this.resourceName\n        }).then(function (data) {\n          _this23.treedata = data;\n        });\n      } else {\n        this.searchForm.parentId = null;\n        this.getTrees();\n      }\n    },\n    getTrees: function getTrees() {\n      var _this24 = this;\n      this.$api[\"systems/getPremissTree\"]({\n        parentId: 0\n      }).then(function (data) {\n        _this24.treedata = data;\n      });\n    }\n  })\n};", {"version": 3, "names": ["<PERSON><PERSON>", "mapActions", "Grid", "_", "commonTree", "checkTree", "iconChoose", "roleUser", "Lazytrees", "dayjs", "defaultSearchForm", "name", "code", "defaultForm", "comments", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "roleType", "roleStatus", "components", "destroyed", "searchEventBus", "$off", "data", "selectModuleName", "moduleNameLists", "loading", "selectedAppList", "appList", "id", "bgurls", "url", "require", "defaultUserProps", "children", "label", "roles", "selectedUserLists", "userLists", "dialogUserVisible", "filterText", "defaultProps", "selectedRoleLists", "roleLists", "dialogRoleVisible", "defaultDeptProps", "selectedDeptLists", "DeptLists", "dialogDeptVisible", "dataForm", "moduleNames", "permissionType", "permissionTypeCode", "permissionTypeCode1", "refIds", "roleId", "allUserDeptIds", "appForm", "appIds", "dataDrawer", "appDrawer", "dialogVisible", "checkStrictly", "ruleForm", "cloneDeep", "rules", "required", "message", "trigger", "min", "max", "drawerName", "drawer", "menuDrawer", "direction", "searchForm", "treeProps", "resourceName", "columns", "title", "key", "tooltip", "min<PERSON><PERSON><PERSON>", "slot", "permissionsList", "tableData", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "treedata", "resourceIds", "roleForm", "<PERSON><PERSON><PERSON><PERSON>", "appLists", "watch", "val", "$refs", "tree", "filter", "mounted", "getRoleLists", "getDeptTreeLists", "getUserLists", "getParamDatas", "methods", "_objectSpread", "goExport", "_this", "$confirm", "confirmButtonText", "cancelButtonText", "type", "then", "s", "Object", "keys", "for<PERSON>ach", "i", "length", "api", "concat", "Date", "format", "downData", "downLoad", "download", "catch", "_this2", "$api", "updateDeptRefIds", "_this3", "item", "push", "getUserData", "e", "r", "primaryCode", "submitData", "_this4", "userId", "startsWith", "substring", "$message", "success", "err", "console", "error", "getAppLists", "_this5", "saveAppLists", "_this6", "_this7", "handleClose", "tag", "index", "findIndex", "splice", "ids", "element", "set<PERSON><PERSON><PERSON><PERSON>eys", "warn", "handleDeptClose", "handleUserClose", "ltree", "_this8", "parentId", "getUserTreeLists", "_this9", "getUserTrees", "node", "datas", "checked<PERSON>eys", "checkedNodes", "getRoleTrees", "getDeptTrees", "filterNode", "value", "indexOf", "filterDeptNode", "organizationName", "_this10", "addRoles", "_this11", "setTimeout", "addUsers", "_this12", "setTimer", "getTime", "selectedNodes", "addDepts", "_this13", "getRoleUser", "_this14", "searchTableData", "getSelectKeys", "_this15", "saveMenu", "_this16", "info", "getBindResourceIds", "_this17", "Number", "$nextTick", "ctree", "setTree", "setDatas", "_this18", "nameIdList", "setApps", "setMenu", "getTrees", "handleAdd", "handleRdit", "JSON", "parse", "stringify", "getParentName", "arr", "param", "_this19", "handleDelete", "_this20", "grid", "query", "submitForm", "formName", "_this21", "validate", "valid", "closeDrawer", "resetFields", "resetForm", "searchTable", "resetTable", "_this22", "getcolumn", "getDatas", "gerTreeData", "_this23", "_this24"], "sources": ["src/bysc_system/views/role/index.vue"], "sourcesContent": ["<!--\r\n * @Author: czw\r\n * @Date: 2022-11-03 17:55:45\r\n * @LastEditors: czw\r\n * @LastEditTime: 2022-12-09 09:26:18\r\n * @FilePath: \\bycloud-vue\\src\\bysc_system\\views\\role\\index.vue\r\n * @Description:\r\n *\r\n * Copyright (c) 2022 by czw/bysc, All Rights Reserved.\r\n-->\r\n<!--  -->\r\n<template>\r\n  <div>\r\n    <el-row>\r\n      <el-col :span=\"24\">\r\n        <Grid\r\n          api=\"systems/rolePage\"\r\n          :event-bus=\"searchEventBus\"\r\n          :search-params=\"searchForm\"\r\n          :newcolumn=\"columns\"\r\n          @datas=\"getDatas\"\r\n          @columnChange=\"getcolumn\"\r\n          ref=\"grid\"\r\n        >\r\n          <div slot=\"search\">\r\n            <el-input\r\n              style=\"width: 200px; margin: 0 10px 0 0\"\r\n              v-model.trim=\"searchForm.name\"\r\n              size=\"small\"\r\n              placeholder=\"请输入名称\"\r\n            ></el-input>\r\n            <el-input\r\n              style=\"width: 200px; margin: 0 10px 0 0\"\r\n              v-model.trim=\"searchForm.code\"\r\n              size=\"small\"\r\n              placeholder=\"请输入标识\"\r\n            ></el-input>\r\n            <el-button\r\n              size=\"small\"\r\n              type=\"primary\"\r\n              style=\"margin: 0 0 0 10px\"\r\n              @click=\"searchTable\"\r\n              >搜索</el-button\r\n            >\r\n            <el-button size=\"small\" @click=\"resetTable\">重置</el-button>\r\n          </div>\r\n          <div slot=\"action\">\r\n            <el-button size=\"small\" type=\"primary\" @click=\"handleAdd\"\r\n              >添加</el-button\r\n            >\r\n            <el-button size=\"small\" type=\"primary\" @click=\"goExport\"\r\n              >导出</el-button\r\n            >\r\n          </div>\r\n          <el-table\r\n            slot=\"table\"\r\n            slot-scope=\"{ loading }\"\r\n            v-loading=\"loading\"\r\n            :data=\"tableData\"\r\n            stripe\r\n            style=\"width: 100%\"\r\n          >\r\n            <el-table-column\r\n              fixed=\"left\"\r\n              :align=\"'center'\"\r\n              type=\"selection\"\r\n              width=\"55\"\r\n            >\r\n            </el-table-column>\r\n            <el-table-column\r\n              fixed=\"left\"\r\n              :align=\"'center'\"\r\n              label=\"序号\"\r\n              type=\"index\"\r\n              width=\"50\"\r\n            >\r\n            </el-table-column>\r\n            <template v-for=\"(item, index) in columns\">\r\n              <el-table-column\r\n                v-if=\"item.slot === 'roleStatus'\"\r\n                :show-overflow-tooltip=\"true\"\r\n                :align=\"item.align ? item.align : 'center'\"\r\n                :key=\"index\"\r\n                :prop=\"item.key\"\r\n                :label=\"item.title\"\r\n                min-width=\"180\"\r\n              >\r\n                <template slot-scope=\"scope\">\r\n                  {{ scope.row[item.slot] ? \"启用\" : \"禁用\" }}\r\n                </template>\r\n              </el-table-column>\r\n              <el-table-column\r\n                v-else-if=\"item.slot === 'roleType'\"\r\n                :show-overflow-tooltip=\"true\"\r\n                :align=\"item.align ? item.align : 'center'\"\r\n                :key=\"index\"\r\n                :prop=\"item.key\"\r\n                :label=\"item.title\"\r\n                min-width=\"180\"\r\n              >\r\n                <template slot-scope=\"scope\">\r\n                  {{ scope.row[item.slot] === 1 ? '超管' :\r\n                     scope.row[item.slot] === 2 ? '管理员' :\r\n                     scope.row[item.slot] === 3 ? '普通用户' : '' }}\r\n                </template>\r\n              </el-table-column>\r\n              <el-table-column\r\n                v-else\r\n                :show-overflow-tooltip=\"true\"\r\n                :key=\"item.key\"\r\n                :prop=\"item.key\"\r\n                :label=\"item.title\"\r\n                :min-width=\"item.width ? item.width : '150'\"\r\n                :align=\"item.align ? item.align : 'center'\"\r\n              >\r\n              </el-table-column>\r\n            </template>\r\n            <el-table-column\r\n              fixed=\"right\"\r\n              align=\"center\"\r\n              label=\"操作\"\r\n              type=\"action\"\r\n              width=\"180\"\r\n            >\r\n              <template slot-scope=\"scope\">\r\n                <el-button @click=\"getRoleUser(scope.row)\" type=\"text\"\r\n                  >角色用户</el-button\r\n                >\r\n                <el-dropdown style=\"margin-left: 6px\">\r\n                  <span class=\"el-dropdown-link\" style=\"font-size: 14px\">\r\n                    更多<i class=\"el-icon-arrow-down el-icon--right\"></i>\r\n                  </span>\r\n                  <el-dropdown-menu slot=\"dropdown\">\r\n                    <el-dropdown-item @click.native=\"handleRdit(scope.row)\">\r\n                      <el-button type=\"text\">编辑</el-button>\r\n                    </el-dropdown-item>\r\n                    <el-dropdown-item @click.native=\"setMenu(scope.row)\">\r\n                      <el-button type=\"text\">资源配置</el-button>\r\n                    </el-dropdown-item>\r\n                    <el-dropdown-item @click.native=\"setDatas(scope.row)\">\r\n                      <el-button type=\"text\">数据配置</el-button>\r\n                    </el-dropdown-item>\r\n                    <el-dropdown-item @click.native=\"setApps(scope.row)\">\r\n                      <el-button type=\"text\">应用配置</el-button>\r\n                    </el-dropdown-item>\r\n                    <el-dropdown-item\r\n                      v-if=\"scope.row.canDelete\"\r\n                      @click.native=\"handleDelete(scope.row.id)\"\r\n                    >\r\n                      <el-button type=\"text\">删除</el-button>\r\n                    </el-dropdown-item>\r\n                  </el-dropdown-menu>\r\n                </el-dropdown>\r\n              </template>\r\n            </el-table-column>\r\n          </el-table>\r\n        </Grid>\r\n      </el-col>\r\n    </el-row>\r\n    <el-drawer\r\n      size=\"50%\"\r\n      title=\"资源配置\"\r\n      :visible.sync=\"menuDrawer\"\r\n      :direction=\"direction\"\r\n    >\r\n      <div style=\"width: 100%; padding: 0 10px; margin-bottom: 100px\">\r\n        <checkTree\r\n          ref=\"ctree\"\r\n          :checkStrictly=\"checkStrictly\"\r\n          :defaultexpandall=\"true\"\r\n          :tree-props=\"treeProps\"\r\n          :tree-data=\"treedata\"\r\n          @treeNode=\"getSelectKeys\"\r\n        ></checkTree>\r\n        <div class=\"demo-drawer-footer\">\r\n          <el-button\r\n            size=\"small\"\r\n            type=\"primary\"\r\n            @click=\"saveMenu()\"\r\n            :loading=\"loading\"\r\n            >保存</el-button\r\n          >\r\n        </div>\r\n      </div>\r\n    </el-drawer>\r\n    <el-drawer\r\n      size=\"50%\"\r\n      :title=\"drawerName\"\r\n      :visible.sync=\"drawer\"\r\n      :direction=\"direction\"\r\n    >\r\n      <div style=\"width: 100%; padding: 0 10px\">\r\n        <el-form\r\n          :model=\"ruleForm\"\r\n          :rules=\"rules\"\r\n          ref=\"ruleForm\"\r\n          label-width=\"100px\"\r\n          class=\"demo-ruleForm\"\r\n        >\r\n          <el-form-item label=\"名称\" prop=\"roleName\">\r\n            <el-input\r\n              size=\"small\"\r\n              maxlength=\"15\"\r\n              v-model.trim=\"ruleForm.roleName\"\r\n            ></el-input>\r\n          </el-form-item>\r\n          <el-form-item label=\"标识\" prop=\"roleKey\">\r\n            <el-input\r\n              size=\"small\"\r\n              maxlength=\"32\"\r\n              v-model.trim=\"ruleForm.roleKey\"\r\n            ></el-input>\r\n          </el-form-item>\r\n\r\n          <el-form-item label=\"角色类型\" prop=\"roleType\">\r\n            <el-select\r\n              v-model=\"ruleForm.roleType\"\r\n              size=\"small\"\r\n              placeholder=\"请选择角色类型\"\r\n            >\r\n              <el-option label=\"超管\" :value=\"1\"></el-option>\r\n              <el-option label=\"管理员\" :value=\"2\"></el-option>\r\n              <el-option label=\"普通用户\" :value=\"3\"></el-option>\r\n            </el-select>\r\n          </el-form-item>\r\n\r\n          <el-form-item label=\"角色描述\" prop=\"comments\">\r\n            <el-input\r\n              type=\"textarea\"\r\n              size=\"small\"\r\n              maxlength=\"200\"\r\n              v-model.trim=\"ruleForm.comments\"\r\n            ></el-input>\r\n          </el-form-item>\r\n          <el-form-item label=\"是否启用\" prop=\"roleStatus\">\r\n            <el-switch v-model.trim=\"roleStatus\"></el-switch>\r\n          </el-form-item>\r\n          <div class=\"demo-drawer-footer\">\r\n            <el-button size=\"small\" @click=\"closeDrawer\">关闭</el-button>\r\n            <el-button\r\n              size=\"small\"\r\n              type=\"primary\"\r\n              @click=\"submitForm('ruleForm')\"\r\n              >保存</el-button\r\n            >\r\n          </div>\r\n        </el-form>\r\n      </div>\r\n    </el-drawer>\r\n    <el-drawer\r\n      size=\"700px\"\r\n      stitle=\"角色用户\"\r\n      :visible.sync=\"roleDrawer\"\r\n      :direction=\"direction\"\r\n    >\r\n      <div style=\"width: 100%; padding: 0 10px\">\r\n        <roleUser ref=\"roleUser\"></roleUser>\r\n      </div>\r\n    </el-drawer>\r\n    <el-drawer\r\n      size=\"700px\"\r\n      title=\"数据配置\"\r\n      :modal=\"false\"\r\n      :visible.sync=\"dataDrawer\"\r\n      :direction=\"direction\"\r\n    >\r\n      <div style=\"width: 100%; padding: 0 10px; position: relative\">\r\n        <!-- <span style=\"font-weight:bold\">数据配置：</span> -->\r\n        <el-radio-group v-model=\"dataForm.permissionType\">\r\n          <el-radio style=\"width: 100%; margin-top: 5px\" :label=\"'ME'\"\r\n            >本人</el-radio\r\n          >\r\n          <!-- <el-radio style=\"width: 100%; margin-top: 5px\" :label=\"'SUBORDINATE'\"\r\n            >本人及下属</el-radio\r\n          > -->\r\n          <!-- <el-radio\r\n            style=\"width: 100%; margin-top: 5px\"\r\n            :label=\"'MY_DEPARTMENT'\"\r\n            >本部门</el-radio\r\n          > -->\r\n          <!-- <el-radio\r\n            style=\"width: 100%; margin-top: 5px\"\r\n            :label=\"'MY_DEPARTMENT_AND_SUB'\"\r\n            >本部门及下属</el-radio\r\n          > -->\r\n          <el-radio style=\"width: 100%; margin-top: 5px\" :label=\"'ALL'\"\r\n            >全部</el-radio\r\n          >\r\n          <el-radio style=\"width: 100%; margin-top: 5px\" :label=\"8\"\r\n            >指定范围</el-radio\r\n          >\r\n        </el-radio-group>\r\n        <div style=\"margin-top: 10px\" v-show=\"dataForm.permissionType === 8\">\r\n          <span>指定范围：</span>\r\n          <el-radio-group v-model=\"dataForm.permissionTypeCode1\">\r\n            <el-radio :label=\"'SPECIFIC_USER'\">指定用户</el-radio>\r\n            <el-radio :label=\"'SPECIFIC_DEPT'\">指定部门/分子公司</el-radio>\r\n            <el-radio :label=\"'SPECIFIC_ROLE'\">指定组织角色</el-radio>\r\n          </el-radio-group>\r\n        </div>\r\n        <div\r\n          v-show=\"dataForm.permissionType === 8\"\r\n          style=\"width: 100%; height: 1px; background: #ccc; margin-top: 15px\"\r\n        ></div>\r\n        <div v-show=\"dataForm.permissionType === 8\" style=\"margin-top: 15px\">\r\n          <div\r\n            style=\"margin-top: 5px\"\r\n            v-show=\"dataForm.permissionTypeCode1 == 'SPECIFIC_USER'\"\r\n          >\r\n            <div style=\"font-weight: bold; margin-bottom: 8px\">指定用户</div>\r\n            <el-button\r\n              style=\"margin-right: 5px\"\r\n              @click=\"addUsers\"\r\n              type=\"primary\"\r\n              size=\"small\"\r\n              icon=\"el-icon-circle-plus-outline\"\r\n              >添加</el-button\r\n            >\r\n            <el-tag\r\n              style=\"margin: 5px\"\r\n              v-for=\"tag in selectedUserLists\"\r\n              :key=\"tag.id\"\r\n              closable\r\n              @close=\"handleUserClose(tag)\"\r\n            >\r\n              {{ tag.label }}\r\n            </el-tag>\r\n          </div>\r\n          <div\r\n            style=\"margin-top: 5px\"\r\n            v-show=\"dataForm.permissionTypeCode1 == 'SPECIFIC_DEPT'\"\r\n          >\r\n            <div style=\"font-weight: bold; margin-bottom: 8px\">指定部门</div>\r\n            <el-button\r\n              style=\"margin-right: 5px\"\r\n              @click=\"addDepts\"\r\n              type=\"primary\"\r\n              size=\"small\"\r\n              icon=\"el-icon-circle-plus-outline\"\r\n              >添加</el-button\r\n            >\r\n            <el-tag\r\n              style=\"margin: 5px\"\r\n              v-for=\"tag in selectedDeptLists\"\r\n              :key=\"tag.id\"\r\n              closable\r\n              @close=\"handleDeptClose(tag)\"\r\n            >\r\n              {{ tag.organizationName }}\r\n            </el-tag>\r\n          </div>\r\n          <div\r\n            style=\"margin-top: 5px\"\r\n            v-show=\"dataForm.permissionTypeCode1 == 'SPECIFIC_ROLE'\"\r\n          >\r\n            <div style=\"font-weight: bold; margin-bottom: 8px\">\r\n              指定组织角色\r\n            </div>\r\n            <el-button\r\n              style=\"margin-right: 5px\"\r\n              @click=\"addRoles\"\r\n              type=\"primary\"\r\n              size=\"small\"\r\n              icon=\"el-icon-circle-plus-outline\"\r\n              >添加</el-button\r\n            >\r\n            <el-tag\r\n              style=\"margin: 5px\"\r\n              v-for=\"tag in selectedRoleLists\"\r\n              :key=\"tag.id\"\r\n              closable\r\n              @close=\"handleClose(tag)\"\r\n            >\r\n              {{ tag.roleName }}\r\n            </el-tag>\r\n          </div>\r\n          <el-dialog\r\n            title=\"指定用户\"\r\n            :modal=\"false\"\r\n            :visible.sync=\"dialogUserVisible\"\r\n            width=\"50%\"\r\n          >\r\n            <div style=\"width: 100%; height: 500px; overflow: auto\">\r\n              <!-- <div style=\"width:100%\">\r\n                <el-input\r\n                  placeholder=\"输入关键字进行过滤\"\r\n                  size=\"small\"\r\n                  style=\"margin-bottom:10px;width:48%\"\r\n                  v-model=\"filterText\">\r\n                </el-input>\r\n              </div> -->\r\n              <div\r\n                style=\"\r\n                  height: 100%;\r\n                  width: 50%;\r\n                  float: left;\r\n                  padding: 10px;\r\n                  overflow-y: scroll;\r\n                \"\r\n              >\r\n                <Lazytrees ref=\"ltree\" @treeNode=\"getUserData\"></Lazytrees>\r\n                <!-- <el-tree\r\n                  :data=\"userLists\"\r\n                  show-checkbox\r\n                  :check-strictly=\"true\"\r\n                  @check=\"getUserTrees\"\r\n                  node-key=\"primaryCode\"\r\n                  ref=\"tree\"\r\n                  :default-expand-all=\"true\"\r\n                  :filter-node-method=\"filterNode\"\r\n                  :props=\"defaultUserProps\">\r\n                </el-tree> -->\r\n              </div>\r\n              <div\r\n                style=\"\r\n                  height: 100%;\r\n                  width: 50%;\r\n                  float: left;\r\n                  padding: 10px;\r\n                  border: 1px solid #f2f2f2;\r\n                \"\r\n              >\r\n                <div\r\n                  style=\"\r\n                    text-align: center;\r\n                    height: 30px;\r\n                    width: 100%;\r\n                    line-height: 30px;\r\n                  \"\r\n                >\r\n                  已选择的用户\r\n                </div>\r\n                <div>\r\n                  <el-tag\r\n                    style=\"margin: 5px\"\r\n                    v-for=\"tag in selectedUserLists\"\r\n                    :key=\"tag.primaryCode\"\r\n                    closable\r\n                    @close=\"handleUserClose(tag)\"\r\n                  >\r\n                    {{ tag.label }}\r\n                  </el-tag>\r\n                </div>\r\n              </div>\r\n            </div>\r\n            <span slot=\"footer\" class=\"dialog-footer\">\r\n              <el-button\r\n                size=\"small\"\r\n                type=\"primary\"\r\n                @click=\"dialogUserVisible = false\"\r\n                >确 定</el-button\r\n              >\r\n            </span>\r\n          </el-dialog>\r\n          <el-dialog\r\n            title=\"选择部门\"\r\n            :modal=\"false\"\r\n            :visible.sync=\"dialogDeptVisible\"\r\n            width=\"50%\"\r\n          >\r\n            <div style=\"width: 100%; height: 500px; overflow: auto\">\r\n              <div style=\"height: 100%; width: 50%; float: left; padding: 10px\">\r\n                <el-input\r\n                  placeholder=\"输入关键字进行过滤\"\r\n                  size=\"small\"\r\n                  style=\"margin-bottom: 10px; width: 90%\"\r\n                  v-model=\"filterText\"\r\n                >\r\n                </el-input>\r\n                <el-tree\r\n                  :data=\"DeptLists\"\r\n                  show-checkbox\r\n                  :check-strictly=\"false\"\r\n                  @check=\"getDeptTrees\"\r\n                  node-key=\"id\"\r\n                  ref=\"tree\"\r\n                  :filter-node-method=\"filterDeptNode\"\r\n                  :props=\"defaultDeptProps\"\r\n                >\r\n                </el-tree>\r\n              </div>\r\n              <div\r\n                style=\"\r\n                  height: 100%;\r\n                  width: 50%;\r\n                  float: left;\r\n                  padding: 10px;\r\n                  border: 1px solid #f2f2f2;\r\n                \"\r\n              >\r\n                <div\r\n                  style=\"\r\n                    text-align: center;\r\n                    height: 30px;\r\n                    width: 100%;\r\n                    line-height: 30px;\r\n                  \"\r\n                >\r\n                  已选择的部门\r\n                </div>\r\n                <div>\r\n                  <el-tag\r\n                    style=\"margin: 5px\"\r\n                    v-for=\"tag in selectedDeptLists\"\r\n                    :key=\"tag.id\"\r\n                    closable\r\n                    @close=\"handleDeptClose(tag)\"\r\n                  >\r\n                    {{ tag.organizationName }}\r\n                  </el-tag>\r\n                </div>\r\n              </div>\r\n            </div>\r\n            <span slot=\"footer\" class=\"dialog-footer\">\r\n              <el-button\r\n                size=\"small\"\r\n                type=\"primary\"\r\n                @click=\"\r\n                  dialogDeptVisible = false;\r\n                  updateDeptRefIds();\r\n                \"\r\n                >确 定</el-button\r\n              >\r\n            </span>\r\n          </el-dialog>\r\n          <el-dialog\r\n            title=\"组织选择角色\"\r\n            :modal=\"false\"\r\n            :visible.sync=\"dialogRoleVisible\"\r\n            width=\"50%\"\r\n          >\r\n            <div style=\"width: 100%; height: 500px; overflow: auto\">\r\n              <div style=\"height: 100%; width: 50%; float: left; padding: 10px\">\r\n                <el-input\r\n                  placeholder=\"输入关键字进行过滤\"\r\n                  size=\"small\"\r\n                  style=\"margin-bottom: 10px; width: 90%\"\r\n                  v-model=\"filterText\"\r\n                >\r\n                </el-input>\r\n                <el-tree\r\n                  :data=\"roleLists\"\r\n                  show-checkbox\r\n                  @check=\"getRoleTrees\"\r\n                  node-key=\"id\"\r\n                  ref=\"tree\"\r\n                  :filter-node-method=\"filterNode\"\r\n                  :props=\"defaultProps\"\r\n                >\r\n                </el-tree>\r\n              </div>\r\n              <div\r\n                style=\"\r\n                  height: 100%;\r\n                  width: 50%;\r\n                  float: left;\r\n                  padding: 10px;\r\n                  border: 1px solid #f2f2f2;\r\n                \"\r\n              >\r\n                <div\r\n                  style=\"\r\n                    text-align: center;\r\n                    height: 30px;\r\n                    width: 100%;\r\n                    line-height: 30px;\r\n                  \"\r\n                >\r\n                  已选择的组织角色\r\n                </div>\r\n                <div>\r\n                  <el-tag\r\n                    style=\"margin: 5px\"\r\n                    v-for=\"tag in selectedRoleLists\"\r\n                    :key=\"tag.id\"\r\n                    closable\r\n                    @close=\"handleClose(tag)\"\r\n                  >\r\n                    {{ tag.roleName }}\r\n                  </el-tag>\r\n                </div>\r\n              </div>\r\n            </div>\r\n            <span slot=\"footer\" class=\"dialog-footer\">\r\n              <el-button\r\n                size=\"small\"\r\n                type=\"primary\"\r\n                @click=\"dialogRoleVisible = false\"\r\n                >确 定</el-button\r\n              >\r\n            </span>\r\n          </el-dialog>\r\n        </div>\r\n        <div style=\"padding-top: 20px\">\r\n          <el-divider content-position=\"left\">生效模块</el-divider>\r\n          <el-checkbox-group v-model=\"dataForm.moduleNames\" size=\"small\">\r\n            <div\r\n              style=\"margin-top: 10px\"\r\n              v-for=\"item in moduleNameLists\"\r\n              :key=\"item.id\"\r\n            >\r\n              <el-checkbox :label=\"item.dictCode\" border>{{\r\n                item.dictName\r\n              }}</el-checkbox>\r\n            </div>\r\n          </el-checkbox-group>\r\n        </div>\r\n\r\n        <div style=\"width: 100%; position: absolute; right: 20px; top: 85vh\">\r\n          <el-button\r\n            style=\"float: right\"\r\n            size=\"small\"\r\n            type=\"primary\"\r\n            @click=\"submitData\"\r\n            :loading=\"loading\"\r\n            >{{ loading ? \"提交中 ...\" : \"确 定\" }}</el-button\r\n          >\r\n        </div>\r\n      </div>\r\n    </el-drawer>\r\n    <!-- 应用配置 -->\r\n    <el-drawer\r\n      size=\"700px\"\r\n      title=\"应用配置\"\r\n      :visible.sync=\"appDrawer\"\r\n      :direction=\"direction\"\r\n    >\r\n      <div style=\"width: 100%; padding: 0 10px\">\r\n        <el-checkbox-group v-model=\"appForm.appIds\">\r\n          <el-row :gutter=\"20\">\r\n            <el-col\r\n              :span=\"6\"\r\n              v-for=\"(item, index) in appLists\"\r\n              :key=\"index + 'app'\"\r\n            >\r\n              <div class=\"appCard\">\r\n                <el-checkbox\r\n                  style=\"position: absolute; left: 3px\"\r\n                  :key=\"item.appId\"\r\n                  :label=\"item.appId\"\r\n                >\r\n                  <div class=\"cardTitle\">{{ item.appName }}</div>\r\n                </el-checkbox>\r\n              </div>\r\n            </el-col>\r\n          </el-row>\r\n        </el-checkbox-group>\r\n        <div>\r\n          <el-button\r\n            style=\"float: right; margin-top: 60px\"\r\n            @click=\"saveAppLists()\"\r\n            type=\"primary\"\r\n            size=\"small\"\r\n          >\r\n            保存\r\n          </el-button>\r\n        </div>\r\n      </div>\r\n    </el-drawer>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport Vue from \"vue\";\r\nimport {mapActions} from \"vuex\";\r\nimport Grid from \"@/components/Grid\";\r\nimport _ from \"lodash\";\r\nimport commonTree from \"@/components/treeComp/commonTree\";\r\nimport checkTree from \"@/components/treeComp/checkTree\";\r\nimport iconChoose from \"@/components/choose/icon-choose\";\r\nimport roleUser from \"@/bysc_system/views/role/roleUser\";\r\nimport Lazytrees from \"@/components/treeComp/ltrees.vue\";\r\nimport dayjs from \"dayjs\";\r\nconst defaultSearchForm = {\r\n  name: \"\",\r\n  code: \"\",\r\n};\r\nconst defaultForm = {\r\n  comments: \"\",\r\n  roleKey: \"\",\r\n  roleName: \"\",\r\n  roleType: \"\",\r\n  roleStatus: 1,\r\n};\r\nexport default {\r\n  components: {commonTree, Grid, iconChoose, checkTree, roleUser, Lazytrees},\r\n  destroyed() {\r\n    this.searchEventBus.$off();\r\n  },\r\n  data() {\r\n    this.searchEventBus = new Vue();\r\n    return {\r\n      selectModuleName: [],\r\n      moduleNameLists: [],\r\n      loading: false,\r\n      selectedAppList: [],\r\n      appList: [\r\n        {\r\n          name: \"工作流\",\r\n          id: 1,\r\n        },\r\n        {\r\n          name: \"系统\",\r\n          id: 2,\r\n        },\r\n        {\r\n          name: \"消息模块\",\r\n          id: 3,\r\n        },\r\n        {\r\n          name: \"库存\",\r\n          id: 4,\r\n        },\r\n        {\r\n          name: \"采购\",\r\n          id: 5,\r\n        },\r\n        {\r\n          name: \"质量\",\r\n          id: 6,\r\n        },\r\n        {\r\n          name: \"运行\",\r\n          id: 7,\r\n        },\r\n      ],\r\n      bgurls: [\r\n        {url: require(\"./images/bg1.png\")},\r\n        {url: require(\"./images/bg2.png\")},\r\n        {url: require(\"./images/bg3.png\")},\r\n      ],\r\n      defaultUserProps: {\r\n        children: \"children\",\r\n        label: \"label\",\r\n        roles: [],\r\n      },\r\n      selectedUserLists: [],\r\n      userLists: [],\r\n      dialogUserVisible: false,\r\n      filterText: \"\",\r\n      defaultProps: {\r\n        children: \"children\",\r\n        label: \"roleName\",\r\n        roles: [],\r\n      },\r\n      selectedRoleLists: [],\r\n      roleLists: [],\r\n      dialogRoleVisible: false,\r\n      defaultDeptProps: {\r\n        children: \"children\",\r\n        label: \"organizationName\",\r\n        roles: [],\r\n      },\r\n      selectedDeptLists: [],\r\n      DeptLists: [],\r\n      dialogDeptVisible: false,\r\n      dataForm: {\r\n        moduleNames: [],\r\n        permissionType: \"\",\r\n        permissionTypeCode: \"\",\r\n        permissionTypeCode1: \"\",\r\n        refIds: [],\r\n        roleId: null,\r\n        allUserDeptIds: [],\r\n      },\r\n      appForm: {\r\n        appIds: [],\r\n        roleId: null,\r\n      },\r\n      dataDrawer: false,\r\n      appDrawer: false,\r\n      dialogVisible: false,\r\n      roleStatus: true,\r\n      checkStrictly: true,\r\n      ruleForm: _.cloneDeep(defaultForm),\r\n      rules: {\r\n        roleName: [\r\n          {required: true, message: \"请输入名称\", trigger: \"blur\"},\r\n          {\r\n            min: 1,\r\n            max: 15,\r\n            message: \"长度在 1 到 15 个字符\",\r\n            trigger: \"blur\",\r\n          },\r\n        ],\r\n        roleKey: [{required: true, message: \"请输入标识\", trigger: \"blur\"}],\r\n        roleType: [\r\n          {required: true, message: \"请选择角色类型\", trigger: \"change\"},\r\n        ],\r\n      },\r\n      drawerName: \"添加\",\r\n      drawer: false,\r\n      menuDrawer: false,\r\n      direction: \"rtl\",\r\n      searchForm: _.cloneDeep(defaultSearchForm),\r\n      treeProps: {\r\n        children: \"children\",\r\n        label: \"resourceName\",\r\n      },\r\n      resourceName: \"\",\r\n      columns: [\r\n        {\r\n          title: \"名称\",\r\n          key: \"roleName\",\r\n          tooltip: true,\r\n          minWidth: 130,\r\n        },\r\n        {\r\n          title: \"标识\",\r\n          key: \"roleKey\",\r\n          minWidth: 150,\r\n          tooltip: true,\r\n        },\r\n        {\r\n          title: \"角色状态\",\r\n          slot: \"roleStatus\",\r\n          tooltip: true,\r\n          minWidth: 170,\r\n        },\r\n\r\n        {\r\n          title: \"角色类型\",\r\n          slot: \"roleType\",\r\n          tooltip: true,\r\n          minWidth: 170,\r\n        },\r\n        {\r\n          title: \"角色描述\",\r\n          key: \"comments\",\r\n          tooltip: true,\r\n          minWidth: 170,\r\n        },\r\n      ],\r\n      permissionsList: [],\r\n      roleName: \"\",\r\n      tableData: [],\r\n      selectedResouce: {},\r\n      treedata: [],\r\n      resourceIds: [],\r\n      roleForm: {\r\n        resourceIds: [],\r\n        roleId: \"\",\r\n      },\r\n      roleId: \"\",\r\n      roleDrawer: false,\r\n      appLists: [],\r\n    };\r\n  },\r\n  watch: {\r\n    roleStatus(val) {\r\n      this.ruleForm.roleStatus = val ? 1 : 0;\r\n    },\r\n    // 'dataForm.permissionTypeCode1'(val) {\r\n    //   if (val === 'SPECIFIC_USER') {\r\n    //     this.selectedUserLists = [];\r\n    //     this.selectedRoleLists = [];\r\n    //   } else if (val === 'SPECIFIC_DEPT') {\r\n    //     this.selectedRoleLists = [];\r\n    //     this.selectedUserLists = [];\r\n    //   } else if (val === 'SPECIFIC_ROLE') {\r\n    //     this.selectedDeptLists = [];\r\n    //     this.selectedUserLists = [];\r\n    //   }\r\n    // },\r\n    filterText(val) {\r\n      this.$refs.tree.filter(val);\r\n    },\r\n  },\r\n  mounted() {\r\n    this.getRoleLists();\r\n    this.getDeptTreeLists();\r\n    this.getUserLists();\r\n    this.getParamDatas();\r\n  },\r\n\r\n  methods: {\r\n    ...mapActions([\"download\"]),\r\n    goExport() {\r\n      this.loading = true;\r\n      this.$confirm(`是否确定导出？`, \"导出\", {\r\n        confirmButtonText: \"确定\",\r\n        cancelButtonText: \"取消\",\r\n        type: \"warning\",\r\n      }).then(() => {\r\n        let s = '';\r\n        Object.keys(this.searchForm).forEach((key, i) => {\r\n          if (this.searchForm[key]) {\r\n            if (i != Object.keys(this.searchForm).length - 1) {\r\n              s = s + key + '=' + this.searchForm[key] + '&';\r\n            } else {\r\n              s = s + key + '=' + this.searchForm[key];\r\n            }\r\n          }\r\n        });\r\n\r\n        this.loading = false;\r\n        let api = '/api/system/role/download';\r\n        let url = `${api}?${s}`;\r\n        let name = `角色管理-${dayjs(new Date()).format('YYYYMMDDHHmmss')}.xlsx`;\r\n        let downData = {\r\n          url: url,\r\n          downLoad: name,\r\n        };\r\n        this.download(downData); // 下载\r\n      })\r\n        .catch(() => {\r\n          this.loading = false;\r\n        });\r\n    },\r\n    getParamDatas() {\r\n      this.$api[\"sysDict/getParam\"]({\r\n        code: \"DATA_PERMISSION_MODULE_NAMES\",\r\n      }).then(data => {\r\n        this.moduleNameLists = data;\r\n      });\r\n    },\r\n    updateDeptRefIds() {\r\n      // 清空之前的 refIds\r\n      this.dataForm.refIds = [];\r\n\r\n      // 从当前选中的部门列表中重新构建 refIds\r\n      this.selectedDeptLists.forEach(item => {\r\n        this.dataForm.refIds.push(item.id);\r\n      });\r\n\r\n      // 记录修改后的 refIds\r\n    },\r\n    getUserData(e) {\r\n      let refIds = [];\r\n      e.forEach(r => {\r\n        refIds.push(r.primaryCode);\r\n      });\r\n      // this.dataForm.refIds = [];\r\n      this.dataForm.allUserDeptIds = refIds;\r\n      this.selectedUserLists = e;\r\n    },\r\n    submitData() {\r\n      this.loading = true;\r\n      if (this.dataForm.permissionType == 8) {\r\n        this.dataForm.permissionTypeCode = this.dataForm.permissionTypeCode1;\r\n\r\n        // 根据不同的权限类型，确保 refIds 是最新的\r\n        if (this.dataForm.permissionTypeCode1 === \"SPECIFIC_DEPT\") {\r\n          // 如果是指定部门，调用 updateDeptRefIds 确保 refIds 是最新的\r\n          this.updateDeptRefIds();\r\n        } else if (this.dataForm.permissionTypeCode1 === \"SPECIFIC_ROLE\") {\r\n          // 如果是指定角色，确保 refIds 是最新的\r\n          this.dataForm.refIds = [];\r\n          this.selectedRoleLists.forEach(item => {\r\n            this.dataForm.refIds.push(item.id);\r\n          });\r\n        } else if (this.dataForm.permissionTypeCode1 === \"SPECIFIC_USER\") {\r\n          // 如果是指定用户，确保 allUserDeptIds 是最新的\r\n          this.dataForm.refIds = [];\r\n          this.selectedUserLists.forEach(item => {\r\n            // 用户ID可能带有 \"user\" 前缀，需要去掉\r\n            const userId = item.primaryCode.startsWith(\"user\")\r\n              ? item.primaryCode.substring(4)\r\n              : item.primaryCode;\r\n            this.dataForm.refIds.push(userId);\r\n          });\r\n        }\r\n      } else {\r\n        this.dataForm.permissionTypeCode = this.dataForm.permissionType;\r\n      }\r\n\r\n      // 打印最终要提交的数据\r\n      this.$api[\"systems/datapermission\"](this.dataForm)\r\n        .then(data => {\r\n          this.$message.success(\"保存成功\");\r\n          this.dataDrawer = false;\r\n          this.loading = false;\r\n        })\r\n        .catch(err => {\r\n          this.loading = false;\r\n          console.error(\"提交数据出错:\", err);\r\n        });\r\n    },\r\n    getAppLists(id) {\r\n      this.$api[\"systems/getAppLists\"]().then(data => {\r\n        this.appLists = data;\r\n        this.$api[\"systems/roleGrantedApps\"]({roleId: id}).then(data => {\r\n          this.appForm.appIds = data;\r\n        });\r\n      });\r\n    },\r\n    saveAppLists(id) {\r\n      this.$api[\"systems/saveRoleApp\"](this.appForm).then(data => {\r\n        this.$message.success(\"应用配置成功\");\r\n        this.appDrawer = false;\r\n      });\r\n    },\r\n    getUserLists() {\r\n      this.$api[\"systems/org-user-tree\"]().then(data => {\r\n        this.userLists = data;\r\n      });\r\n    },\r\n    handleClose(tag) {\r\n      let index = this.selectedRoleLists.findIndex(e => {\r\n        return e.id === tag.id;\r\n      });\r\n      this.selectedRoleLists.splice(index, 1);\r\n      let ids = [];\r\n      this.selectedRoleLists.forEach(element => {\r\n        ids.push(element.id);\r\n      });\r\n\r\n      // 添加安全检查，确保 this.$refs.tree 存在\r\n      if (this.$refs.tree) {\r\n        this.$refs.tree.setCheckedKeys(ids);\r\n      } else {\r\n        console.warn(\"this.$refs.tree is undefined in handleClose\");\r\n      }\r\n\r\n      // 重新设置 dataForm.refIds，确保只包含当前选中的角色 ID\r\n      this.dataForm.refIds = [...ids];\r\n    },\r\n    handleDeptClose(tag) {\r\n      let index = this.selectedDeptLists.findIndex(e => {\r\n        return e.id === tag.id;\r\n      });\r\n      this.selectedDeptLists.splice(index, 1);\r\n      let ids = [];\r\n      this.selectedDeptLists.forEach(element => {\r\n        ids.push(element.id);\r\n      });\r\n\r\n      // 添加安全检查，确保 this.$refs.tree 存在\r\n      if (this.$refs.tree) {\r\n        this.$refs.tree.setCheckedKeys(ids);\r\n      } else {\r\n        console.warn(\"this.$refs.tree is undefined in handleDeptClose\");\r\n      }\r\n\r\n      // 调用 updateDeptRefIds 函数，同步更新 dataForm.refIds\r\n      this.updateDeptRefIds();\r\n    },\r\n    handleUserClose(tag) {\r\n      let index = this.selectedUserLists.findIndex(e => {\r\n        return e.primaryCode === tag.primaryCode;\r\n      });\r\n      this.selectedUserLists.splice(index, 1);\r\n      let ids = [];\r\n      this.selectedUserLists.forEach(element => {\r\n        ids.push(element.primaryCode);\r\n      });\r\n\r\n      // 添加安全检查，确保 this.$refs.ltree 存在\r\n      if (this.$refs.ltree) {\r\n        this.$refs.ltree.setCheckedKeys(ids);\r\n      } else {\r\n        console.warn(\"this.$refs.ltree is undefined in handleUserClose\");\r\n      }\r\n\r\n      // 重新设置 dataForm.allUserDeptIds，确保只包含当前选中的用户 ID\r\n      this.dataForm.allUserDeptIds = [...ids];\r\n    },\r\n    getDeptTreeLists() {\r\n      this.$api[\"systems/organizationTree\"]({parentId: 0}).then(data => {\r\n        this.DeptLists = data;\r\n      });\r\n    },\r\n    getUserTreeLists() {\r\n      this.$api[\"systems/organizationTree\"]({parentId: 0}).then(data => {\r\n        this.DeptLists = data;\r\n      });\r\n    },\r\n    getUserTrees(node, datas) {\r\n      this.dataForm.allUserDeptIds = datas.checkedKeys;\r\n      this.selectedUserLists = datas.checkedNodes;\r\n    },\r\n    getRoleTrees(node, datas) {\r\n      this.dataForm.refIds = datas.checkedKeys;\r\n      this.selectedRoleLists = datas.checkedNodes;\r\n    },\r\n    getDeptTrees(node, datas) {\r\n      // 先清空 selectedDeptLists\r\n      this.selectedDeptLists = datas.checkedNodes;\r\n      // 调用 updateDeptRefIds 函数，同步更新 dataForm.refIds\r\n      this.updateDeptRefIds();\r\n    },\r\n    filterNode(value, data) {\r\n      if (!value) {\r\n        return true;\r\n      }\r\n      return data.roleName.indexOf(value) !== -1;\r\n    },\r\n    filterDeptNode(value, data) {\r\n      if (!value) {\r\n        return true;\r\n      }\r\n      return data.organizationName.indexOf(value) !== -1;\r\n    },\r\n    getRoleLists() {\r\n      this.$api[\"systems/roleList\"]().then(data => {\r\n        this.roleLists = data;\r\n      });\r\n    },\r\n    addRoles() {\r\n      this.dialogRoleVisible = true;\r\n      setTimeout(() => {\r\n        var ids = [];\r\n        this.selectedRoleLists.forEach(e => {\r\n          ids.push(e.id);\r\n        });\r\n        this.$refs.tree.setCheckedKeys(ids);\r\n      }, 100);\r\n    },\r\n    addUsers() {\r\n      this.dialogUserVisible = true;\r\n      setTimeout(() => {\r\n        this.$refs.ltree.setTimer = new Date().getTime();\r\n        var ids = [];\r\n        this.selectedUserLists.forEach(e => {\r\n          ids.push(e.primaryCode);\r\n        });\r\n        this.$refs.ltree.setCheckedKeys(ids);\r\n        this.$refs.ltree.selectedNodes = this.selectedUserLists;\r\n        // console.log(this.$refs.ltree.selectedNode, \"设置\");\r\n      }, 100);\r\n    },\r\n    addDepts() {\r\n      this.dialogDeptVisible = true;\r\n\r\n      setTimeout(() => {\r\n        var ids = [];\r\n        this.selectedDeptLists.forEach(e => {\r\n          ids.push(e.id);\r\n        });\r\n        this.$refs.tree.setCheckedKeys(ids);\r\n      }, 100);\r\n    },\r\n    getRoleUser(e) {\r\n      this.roleDrawer = true;\r\n      setTimeout(() => {\r\n        this.$refs.roleUser.searchForm.roleId = e.id;\r\n        this.$refs.roleUser.searchTableData();\r\n      }, 50);\r\n    },\r\n    getSelectKeys(e) {\r\n      this.roleForm.resourceIds = [];\r\n      e\r\n        && e.forEach(element => {\r\n          this.roleForm.resourceIds.push(element.id + \"\");\r\n        });\r\n    },\r\n    saveMenu() {\r\n      // roleresourcesave\r\n      if (!this.roleForm.resourceIds.length) {\r\n        this.$message.info(\"请选择对应资源在保存\");\r\n      } else {\r\n        this.$api[\"systems/roleresourcesave\"](this.roleForm).then(data => {\r\n          this.$message({\r\n            message: \"保存成功\",\r\n            type: \"success\",\r\n          });\r\n          this.menuDrawer = false;\r\n        });\r\n      }\r\n    },\r\n    getBindResourceIds() {\r\n      this.$api[\"systems/getResourceIds\"]({id: this.roleForm.roleId}).then(\r\n        data => {\r\n          this.resourceIds = [];\r\n          this.roleForm.resourceIds = [];\r\n          data.forEach(e => {\r\n            this.resourceIds.push(Number(e));\r\n            this.roleForm.resourceIds.push(Number(e));\r\n          });\r\n          this.checkStrictly = true;\r\n          this.$nextTick(() => {\r\n            this.$refs.ctree.setTree(this.resourceIds);\r\n            setTimeout(() => {\r\n              this.checkStrictly = false;\r\n            }, 500);\r\n          });\r\n        }\r\n      );\r\n    },\r\n    setDatas(e) {\r\n      this.$api[\"systems/permissionquery\"]({roleId: e.id}).then(data => {\r\n        this.selectModuleName = [];\r\n        this.dataDrawer = true;\r\n        if (data) {\r\n          if (\r\n            data.permissionTypeCode == \"SPECIFIC_DEPT\"\r\n            || data.permissionTypeCode == \"SPECIFIC_USER\"\r\n            || data.permissionTypeCode == \"SPECIFIC_ROLE\"\r\n          ) {\r\n            this.dataForm.permissionType = 8;\r\n            this.dataForm.permissionTypeCode1 = data.permissionTypeCode;\r\n            this.dataForm.permissionTypeCode = data.permissionTypeCode;\r\n            this.dataForm.refIds = [];\r\n            this.selectedDeptLists = [];\r\n            this.selectedRoleLists = [];\r\n            this.selectedUserLists = [];\r\n            data.nameIdList.forEach(e => {\r\n              if (data.permissionTypeCode === \"SPECIFIC_USER\") {\r\n                this.selectedUserLists.push({\r\n                  primaryCode: \"user\" + e.id,\r\n                  label: e.name,\r\n                });\r\n              } else if (data.permissionTypeCode === \"SPECIFIC_DEPT\") {\r\n                this.selectedDeptLists.push({\r\n                  organizationName: e.name,\r\n                  id: e.id,\r\n                });\r\n              } else if (data.permissionTypeCode === \"SPECIFIC_ROLE\") {\r\n                this.selectedRoleLists.push({id: e.id, roleName: e.name});\r\n              }\r\n              this.dataForm.refIds.push(e.id);\r\n            });\r\n          } else {\r\n            this.dataForm.permissionType = data.permissionTypeCode;\r\n            this.dataForm.permissionTypeCode = data.permissionTypeCode;\r\n            this.dataForm.refIds = [];\r\n          }\r\n          this.dataForm.moduleNames = data.moduleNames ? data.moduleNames : [];\r\n        } else {\r\n          this.selectedDeptLists = [];\r\n          this.selectedRoleLists = [];\r\n          this.selectedUserLists = [];\r\n          this.dataForm = {\r\n            moduleNames: [],\r\n            permissionType: \"\",\r\n            permissionTypeCode: \"\",\r\n            permissionTypeCode1: \"\",\r\n            refIds: [],\r\n            roleId: null,\r\n          };\r\n        }\r\n        this.dataForm.roleId = e.id;\r\n      });\r\n    },\r\n    setApps(e) {\r\n      this.appDrawer = true;\r\n      this.appForm.roleId = e.id;\r\n      this.getAppLists(e.id);\r\n    },\r\n    setMenu(e) {\r\n      this.menuDrawer = true;\r\n      this.roleForm.roleId = e.id;\r\n      this.getTrees();\r\n      this.getBindResourceIds();\r\n    },\r\n    handleAdd() {\r\n      this.drawer = true;\r\n      this.drawerName = \"添加\";\r\n      this.ruleForm = _.cloneDeep(defaultForm);\r\n      this.roleStatus = true;\r\n    },\r\n    handleRdit(data) {\r\n      this.ruleForm = JSON.parse(JSON.stringify(data));\r\n      this.roleStatus = !!data.roleStatus;\r\n      this.drawer = true;\r\n      this.drawerName = \"编辑\";\r\n    },\r\n    getParentName(arr, param) {\r\n      let name = \"\";\r\n      arr.forEach(e => {\r\n        if (e.children) {\r\n          if (e.id == param) {\r\n            name = e.roleName;\r\n          }\r\n          this.getParentName(e.children, param);\r\n        } else {\r\n          if (e.id == param) {\r\n            name = e.roleName;\r\n          }\r\n        }\r\n      });\r\n      return name;\r\n    },\r\n    handleDelete(e) {\r\n      this.$confirm(\"您确定要删除该角色吗？\", \"提示\", {\r\n        confirmButtonText: \"确定\",\r\n        cancelButtonText: \"取消\",\r\n        type: \"warning\",\r\n      })\r\n        .then(() => {\r\n          this.$api[\"systems/roleDelete\"]({id: e}).then(data => {\r\n            this.$refs.grid.query();\r\n            this.$message({\r\n              message: \"删除成功\",\r\n              type: \"success\",\r\n            });\r\n          });\r\n        })\r\n        .catch(() => {});\r\n    },\r\n    submitForm(formName) {\r\n      this.$refs[formName].validate(valid => {\r\n        if (valid) {\r\n          this.loading = true;\r\n          this.$api[\"systems/addRole\"](this.ruleForm).then(data => {\r\n            this.drawer = false;\r\n            this.loading = false;\r\n            this.$message({\r\n              type: \"success\",\r\n              message: \"保存成功\",\r\n            });\r\n            this.$refs.grid.query();\r\n          });\r\n        } else {\r\n          return false;\r\n        }\r\n      });\r\n    },\r\n    closeDrawer() {\r\n      this.ruleForm = _.cloneDeep(defaultForm);\r\n      this.$refs.ruleForm.resetFields();\r\n      this.drawer = false;\r\n    },\r\n    resetForm(formName) {\r\n      this.$refs[formName].resetFields();\r\n    },\r\n    searchTable() {\r\n      this.$refs.grid.query();\r\n    },\r\n    resetTable() {\r\n      this.searchForm = _.cloneDeep(defaultSearchForm);\r\n      this.$nextTick(() => {\r\n        this.$refs.grid.query();\r\n      });\r\n    },\r\n    getcolumn(e) {\r\n      this.columns = e;\r\n    },\r\n    getDatas(e) {\r\n      this.tableData = e;\r\n    },\r\n    gerTreeData() {\r\n      if (this.resourceName) {\r\n        this.$api[\"systems/getTreeList\"]({\r\n          resourceName: this.resourceName,\r\n        }).then(data => {\r\n          this.treedata = data;\r\n        });\r\n      } else {\r\n        this.searchForm.parentId = null;\r\n        this.getTrees();\r\n      }\r\n    },\r\n    getTrees() {\r\n      this.$api[\"systems/getPremissTree\"]({parentId: 0}).then(data => {\r\n        this.treedata = data;\r\n      });\r\n    },\r\n  },\r\n};\r\n</script>\r\n<style lang=\"less\" scoped>\r\n.demo-drawer-footer {\r\n  width: 100%;\r\n  position: absolute;\r\n  bottom: 0;\r\n  left: 0;\r\n  border-top: 1px solid #e8e8e8;\r\n  padding: 10px 16px;\r\n  text-align: right;\r\n  background: #fff;\r\n  z-index: 100;\r\n}\r\n.el-dropdown-link {\r\n  cursor: pointer;\r\n  color: #409eff;\r\n}\r\n.el-icon-arrow-down {\r\n  font-size: 12px;\r\n}\r\n.appCard {\r\n  width: 100%;\r\n  overflow: hidden;\r\n  height: 60px;\r\n  margin-bottom: 10px;\r\n  position: relative;\r\n  border: 1px solid #ccc;\r\n  border-radius: 5px;\r\n}\r\n.cardTitle {\r\n  position: absolute;\r\n  z-index: 999;\r\n  width: 150px;\r\n  text-align: center;\r\n  font-weight: bold;\r\n  height: 60px;\r\n  line-height: 60px;\r\n  top: 0px;\r\n  left: 0;\r\n}\r\n</style>\r\n"], "mappings": ";;;;;;;;;;AAupBA,OAAAA,GAAA;AACA,SAAAC,UAAA;AACA,OAAAC,IAAA;AACA,OAAAC,CAAA;AACA,OAAAC,UAAA;AACA,OAAAC,SAAA;AACA,OAAAC,UAAA;AACA,OAAAC,QAAA;AACA,OAAAC,SAAA;AACA,OAAAC,KAAA;AACA,IAAAC,iBAAA;EACAC,IAAA;EACAC,IAAA;AACA;AACA,IAAAC,WAAA;EACAC,QAAA;EACAC,OAAA;EACAC,QAAA;EACAC,QAAA;EACAC,UAAA;AACA;AACA;EACAC,UAAA;IAAAf,UAAA,EAAAA,UAAA;IAAAF,IAAA,EAAAA,IAAA;IAAAI,UAAA,EAAAA,UAAA;IAAAD,SAAA,EAAAA,SAAA;IAAAE,QAAA,EAAAA,QAAA;IAAAC,SAAA,EAAAA;EAAA;EACAY,SAAA,WAAAA,UAAA;IACA,KAAAC,cAAA,CAAAC,IAAA;EACA;EACAC,IAAA,WAAAA,KAAA;IACA,KAAAF,cAAA,OAAArB,GAAA;IACA;MACAwB,gBAAA;MACAC,eAAA;MACAC,OAAA;MACAC,eAAA;MACAC,OAAA,GACA;QACAjB,IAAA;QACAkB,EAAA;MACA,GACA;QACAlB,IAAA;QACAkB,EAAA;MACA,GACA;QACAlB,IAAA;QACAkB,EAAA;MACA,GACA;QACAlB,IAAA;QACAkB,EAAA;MACA,GACA;QACAlB,IAAA;QACAkB,EAAA;MACA,GACA;QACAlB,IAAA;QACAkB,EAAA;MACA,GACA;QACAlB,IAAA;QACAkB,EAAA;MACA,EACA;MACAC,MAAA,GACA;QAAAC,GAAA,EAAAC,OAAA;MAAA,GACA;QAAAD,GAAA,EAAAC,OAAA;MAAA,GACA;QAAAD,GAAA,EAAAC,OAAA;MAAA,EACA;MACAC,gBAAA;QACAC,QAAA;QACAC,KAAA;QACAC,KAAA;MACA;MACAC,iBAAA;MACAC,SAAA;MACAC,iBAAA;MACAC,UAAA;MACAC,YAAA;QACAP,QAAA;QACAC,KAAA;QACAC,KAAA;MACA;MACAM,iBAAA;MACAC,SAAA;MACAC,iBAAA;MACAC,gBAAA;QACAX,QAAA;QACAC,KAAA;QACAC,KAAA;MACA;MACAU,iBAAA;MACAC,SAAA;MACAC,iBAAA;MACAC,QAAA;QACAC,WAAA;QACAC,cAAA;QACAC,kBAAA;QACAC,mBAAA;QACAC,MAAA;QACAC,MAAA;QACAC,cAAA;MACA;MACAC,OAAA;QACAC,MAAA;QACAH,MAAA;MACA;MACAI,UAAA;MACAC,SAAA;MACAC,aAAA;MACA3C,UAAA;MACA4C,aAAA;MACAC,QAAA,EAAA5D,CAAA,CAAA6D,SAAA,CAAAnD,WAAA;MACAoD,KAAA;QACAjD,QAAA,GACA;UAAAkD,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA,GACA;UACAC,GAAA;UACAC,GAAA;UACAH,OAAA;UACAC,OAAA;QACA,EACA;QACArD,OAAA;UAAAmD,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA;QACAnD,QAAA,GACA;UAAAiD,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA;MAEA;MACAG,UAAA;MACAC,MAAA;MACAC,UAAA;MACAC,SAAA;MACAC,UAAA,EAAAxE,CAAA,CAAA6D,SAAA,CAAAtD,iBAAA;MACAkE,SAAA;QACA1C,QAAA;QACAC,KAAA;MACA;MACA0C,YAAA;MACAC,OAAA,GACA;QACAC,KAAA;QACAC,GAAA;QACAC,OAAA;QACAC,QAAA;MACA,GACA;QACAH,KAAA;QACAC,GAAA;QACAE,QAAA;QACAD,OAAA;MACA,GACA;QACAF,KAAA;QACAI,IAAA;QACAF,OAAA;QACAC,QAAA;MACA,GAEA;QACAH,KAAA;QACAI,IAAA;QACAF,OAAA;QACAC,QAAA;MACA,GACA;QACAH,KAAA;QACAC,GAAA;QACAC,OAAA;QACAC,QAAA;MACA,EACA;MACAE,eAAA;MACApE,QAAA;MACAqE,SAAA;MACAC,eAAA;MACAC,QAAA;MACAC,WAAA;MACAC,QAAA;QACAD,WAAA;QACAjC,MAAA;MACA;MACAA,MAAA;MACAmC,UAAA;MACAC,QAAA;IACA;EACA;EACAC,KAAA;IACA1E,UAAA,WAAAA,WAAA2E,GAAA;MACA,KAAA9B,QAAA,CAAA7C,UAAA,GAAA2E,GAAA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACArD,UAAA,WAAAA,WAAAqD,GAAA;MACA,KAAAC,KAAA,CAAAC,IAAA,CAAAC,MAAA,CAAAH,GAAA;IACA;EACA;EACAI,OAAA,WAAAA,QAAA;IACA,KAAAC,YAAA;IACA,KAAAC,gBAAA;IACA,KAAAC,YAAA;IACA,KAAAC,aAAA;EACA;EAEAC,OAAA,EAAAC,aAAA,CAAAA,aAAA,KACAtG,UAAA;IACAuG,QAAA,WAAAA,SAAA;MAAA,IAAAC,KAAA;MACA,KAAA/E,OAAA;MACA,KAAAgF,QAAA;QACAC,iBAAA;QACAC,gBAAA;QACAC,IAAA;MACA,GAAAC,IAAA;QACA,IAAAC,CAAA;QACAC,MAAA,CAAAC,IAAA,CAAAR,KAAA,CAAA9B,UAAA,EAAAuC,OAAA,WAAAlC,GAAA,EAAAmC,CAAA;UACA,IAAAV,KAAA,CAAA9B,UAAA,CAAAK,GAAA;YACA,IAAAmC,CAAA,IAAAH,MAAA,CAAAC,IAAA,CAAAR,KAAA,CAAA9B,UAAA,EAAAyC,MAAA;cACAL,CAAA,GAAAA,CAAA,GAAA/B,GAAA,SAAAyB,KAAA,CAAA9B,UAAA,CAAAK,GAAA;YACA;cACA+B,CAAA,GAAAA,CAAA,GAAA/B,GAAA,SAAAyB,KAAA,CAAA9B,UAAA,CAAAK,GAAA;YACA;UACA;QACA;QAEAyB,KAAA,CAAA/E,OAAA;QACA,IAAA2F,GAAA;QACA,IAAAtF,GAAA,MAAAuF,MAAA,CAAAD,GAAA,OAAAC,MAAA,CAAAP,CAAA;QACA,IAAApG,IAAA,+BAAA2G,MAAA,CAAA7G,KAAA,KAAA8G,IAAA,IAAAC,MAAA;QACA,IAAAC,QAAA;UACA1F,GAAA,EAAAA,GAAA;UACA2F,QAAA,EAAA/G;QACA;QACA8F,KAAA,CAAAkB,QAAA,CAAAF,QAAA;MACA,GACAG,KAAA;QACAnB,KAAA,CAAA/E,OAAA;MACA;IACA;IACA2E,aAAA,WAAAA,cAAA;MAAA,IAAAwB,MAAA;MACA,KAAAC,IAAA;QACAlH,IAAA;MACA,GAAAkG,IAAA,WAAAvF,IAAA;QACAsG,MAAA,CAAApG,eAAA,GAAAF,IAAA;MACA;IACA;IACAwG,gBAAA,WAAAA,iBAAA;MAAA,IAAAC,MAAA;MACA;MACA,KAAA/E,QAAA,CAAAK,MAAA;;MAEA;MACA,KAAAR,iBAAA,CAAAoE,OAAA,WAAAe,IAAA;QACAD,MAAA,CAAA/E,QAAA,CAAAK,MAAA,CAAA4E,IAAA,CAAAD,IAAA,CAAApG,EAAA;MACA;;MAEA;IACA;IACAsG,WAAA,WAAAA,YAAAC,CAAA;MACA,IAAA9E,MAAA;MACA8E,CAAA,CAAAlB,OAAA,WAAAmB,CAAA;QACA/E,MAAA,CAAA4E,IAAA,CAAAG,CAAA,CAAAC,WAAA;MACA;MACA;MACA,KAAArF,QAAA,CAAAO,cAAA,GAAAF,MAAA;MACA,KAAAjB,iBAAA,GAAA+F,CAAA;IACA;IACAG,UAAA,WAAAA,WAAA;MAAA,IAAAC,MAAA;MACA,KAAA9G,OAAA;MACA,SAAAuB,QAAA,CAAAE,cAAA;QACA,KAAAF,QAAA,CAAAG,kBAAA,QAAAH,QAAA,CAAAI,mBAAA;;QAEA;QACA,SAAAJ,QAAA,CAAAI,mBAAA;UACA;UACA,KAAA0E,gBAAA;QACA,gBAAA9E,QAAA,CAAAI,mBAAA;UACA;UACA,KAAAJ,QAAA,CAAAK,MAAA;UACA,KAAAZ,iBAAA,CAAAwE,OAAA,WAAAe,IAAA;YACAO,MAAA,CAAAvF,QAAA,CAAAK,MAAA,CAAA4E,IAAA,CAAAD,IAAA,CAAApG,EAAA;UACA;QACA,gBAAAoB,QAAA,CAAAI,mBAAA;UACA;UACA,KAAAJ,QAAA,CAAAK,MAAA;UACA,KAAAjB,iBAAA,CAAA6E,OAAA,WAAAe,IAAA;YACA;YACA,IAAAQ,MAAA,GAAAR,IAAA,CAAAK,WAAA,CAAAI,UAAA,WACAT,IAAA,CAAAK,WAAA,CAAAK,SAAA,MACAV,IAAA,CAAAK,WAAA;YACAE,MAAA,CAAAvF,QAAA,CAAAK,MAAA,CAAA4E,IAAA,CAAAO,MAAA;UACA;QACA;MACA;QACA,KAAAxF,QAAA,CAAAG,kBAAA,QAAAH,QAAA,CAAAE,cAAA;MACA;;MAEA;MACA,KAAA2E,IAAA,gCAAA7E,QAAA,EACA6D,IAAA,WAAAvF,IAAA;QACAiH,MAAA,CAAAI,QAAA,CAAAC,OAAA;QACAL,MAAA,CAAA7E,UAAA;QACA6E,MAAA,CAAA9G,OAAA;MACA,GACAkG,KAAA,WAAAkB,GAAA;QACAN,MAAA,CAAA9G,OAAA;QACAqH,OAAA,CAAAC,KAAA,YAAAF,GAAA;MACA;IACA;IACAG,WAAA,WAAAA,YAAApH,EAAA;MAAA,IAAAqH,MAAA;MACA,KAAApB,IAAA,0BAAAhB,IAAA,WAAAvF,IAAA;QACA2H,MAAA,CAAAvD,QAAA,GAAApE,IAAA;QACA2H,MAAA,CAAApB,IAAA;UAAAvE,MAAA,EAAA1B;QAAA,GAAAiF,IAAA,WAAAvF,IAAA;UACA2H,MAAA,CAAAzF,OAAA,CAAAC,MAAA,GAAAnC,IAAA;QACA;MACA;IACA;IACA4H,YAAA,WAAAA,aAAAtH,EAAA;MAAA,IAAAuH,MAAA;MACA,KAAAtB,IAAA,6BAAArE,OAAA,EAAAqD,IAAA,WAAAvF,IAAA;QACA6H,MAAA,CAAAR,QAAA,CAAAC,OAAA;QACAO,MAAA,CAAAxF,SAAA;MACA;IACA;IACAwC,YAAA,WAAAA,aAAA;MAAA,IAAAiD,MAAA;MACA,KAAAvB,IAAA,4BAAAhB,IAAA,WAAAvF,IAAA;QACA8H,MAAA,CAAA/G,SAAA,GAAAf,IAAA;MACA;IACA;IACA+H,WAAA,WAAAA,YAAAC,GAAA;MACA,IAAAC,KAAA,QAAA9G,iBAAA,CAAA+G,SAAA,WAAArB,CAAA;QACA,OAAAA,CAAA,CAAAvG,EAAA,KAAA0H,GAAA,CAAA1H,EAAA;MACA;MACA,KAAAa,iBAAA,CAAAgH,MAAA,CAAAF,KAAA;MACA,IAAAG,GAAA;MACA,KAAAjH,iBAAA,CAAAwE,OAAA,WAAA0C,OAAA;QACAD,GAAA,CAAAzB,IAAA,CAAA0B,OAAA,CAAA/H,EAAA;MACA;;MAEA;MACA,SAAAiE,KAAA,CAAAC,IAAA;QACA,KAAAD,KAAA,CAAAC,IAAA,CAAA8D,cAAA,CAAAF,GAAA;MACA;QACAZ,OAAA,CAAAe,IAAA;MACA;;MAEA;MACA,KAAA7G,QAAA,CAAAK,MAAA,MAAAgE,MAAA,CAAAqC,GAAA;IACA;IACAI,eAAA,WAAAA,gBAAAR,GAAA;MACA,IAAAC,KAAA,QAAA1G,iBAAA,CAAA2G,SAAA,WAAArB,CAAA;QACA,OAAAA,CAAA,CAAAvG,EAAA,KAAA0H,GAAA,CAAA1H,EAAA;MACA;MACA,KAAAiB,iBAAA,CAAA4G,MAAA,CAAAF,KAAA;MACA,IAAAG,GAAA;MACA,KAAA7G,iBAAA,CAAAoE,OAAA,WAAA0C,OAAA;QACAD,GAAA,CAAAzB,IAAA,CAAA0B,OAAA,CAAA/H,EAAA;MACA;;MAEA;MACA,SAAAiE,KAAA,CAAAC,IAAA;QACA,KAAAD,KAAA,CAAAC,IAAA,CAAA8D,cAAA,CAAAF,GAAA;MACA;QACAZ,OAAA,CAAAe,IAAA;MACA;;MAEA;MACA,KAAA/B,gBAAA;IACA;IACAiC,eAAA,WAAAA,gBAAAT,GAAA;MACA,IAAAC,KAAA,QAAAnH,iBAAA,CAAAoH,SAAA,WAAArB,CAAA;QACA,OAAAA,CAAA,CAAAE,WAAA,KAAAiB,GAAA,CAAAjB,WAAA;MACA;MACA,KAAAjG,iBAAA,CAAAqH,MAAA,CAAAF,KAAA;MACA,IAAAG,GAAA;MACA,KAAAtH,iBAAA,CAAA6E,OAAA,WAAA0C,OAAA;QACAD,GAAA,CAAAzB,IAAA,CAAA0B,OAAA,CAAAtB,WAAA;MACA;;MAEA;MACA,SAAAxC,KAAA,CAAAmE,KAAA;QACA,KAAAnE,KAAA,CAAAmE,KAAA,CAAAJ,cAAA,CAAAF,GAAA;MACA;QACAZ,OAAA,CAAAe,IAAA;MACA;;MAEA;MACA,KAAA7G,QAAA,CAAAO,cAAA,MAAA8D,MAAA,CAAAqC,GAAA;IACA;IACAxD,gBAAA,WAAAA,iBAAA;MAAA,IAAA+D,MAAA;MACA,KAAApC,IAAA;QAAAqC,QAAA;MAAA,GAAArD,IAAA,WAAAvF,IAAA;QACA2I,MAAA,CAAAnH,SAAA,GAAAxB,IAAA;MACA;IACA;IACA6I,gBAAA,WAAAA,iBAAA;MAAA,IAAAC,MAAA;MACA,KAAAvC,IAAA;QAAAqC,QAAA;MAAA,GAAArD,IAAA,WAAAvF,IAAA;QACA8I,MAAA,CAAAtH,SAAA,GAAAxB,IAAA;MACA;IACA;IACA+I,YAAA,WAAAA,aAAAC,IAAA,EAAAC,KAAA;MACA,KAAAvH,QAAA,CAAAO,cAAA,GAAAgH,KAAA,CAAAC,WAAA;MACA,KAAApI,iBAAA,GAAAmI,KAAA,CAAAE,YAAA;IACA;IACAC,YAAA,WAAAA,aAAAJ,IAAA,EAAAC,KAAA;MACA,KAAAvH,QAAA,CAAAK,MAAA,GAAAkH,KAAA,CAAAC,WAAA;MACA,KAAA/H,iBAAA,GAAA8H,KAAA,CAAAE,YAAA;IACA;IACAE,YAAA,WAAAA,aAAAL,IAAA,EAAAC,KAAA;MACA;MACA,KAAA1H,iBAAA,GAAA0H,KAAA,CAAAE,YAAA;MACA;MACA,KAAA3C,gBAAA;IACA;IACA8C,UAAA,WAAAA,WAAAC,KAAA,EAAAvJ,IAAA;MACA,KAAAuJ,KAAA;QACA;MACA;MACA,OAAAvJ,IAAA,CAAAP,QAAA,CAAA+J,OAAA,CAAAD,KAAA;IACA;IACAE,cAAA,WAAAA,eAAAF,KAAA,EAAAvJ,IAAA;MACA,KAAAuJ,KAAA;QACA;MACA;MACA,OAAAvJ,IAAA,CAAA0J,gBAAA,CAAAF,OAAA,CAAAD,KAAA;IACA;IACA5E,YAAA,WAAAA,aAAA;MAAA,IAAAgF,OAAA;MACA,KAAApD,IAAA,uBAAAhB,IAAA,WAAAvF,IAAA;QACA2J,OAAA,CAAAvI,SAAA,GAAApB,IAAA;MACA;IACA;IACA4J,QAAA,WAAAA,SAAA;MAAA,IAAAC,OAAA;MACA,KAAAxI,iBAAA;MACAyI,UAAA;QACA,IAAA1B,GAAA;QACAyB,OAAA,CAAA1I,iBAAA,CAAAwE,OAAA,WAAAkB,CAAA;UACAuB,GAAA,CAAAzB,IAAA,CAAAE,CAAA,CAAAvG,EAAA;QACA;QACAuJ,OAAA,CAAAtF,KAAA,CAAAC,IAAA,CAAA8D,cAAA,CAAAF,GAAA;MACA;IACA;IACA2B,QAAA,WAAAA,SAAA;MAAA,IAAAC,OAAA;MACA,KAAAhJ,iBAAA;MACA8I,UAAA;QACAE,OAAA,CAAAzF,KAAA,CAAAmE,KAAA,CAAAuB,QAAA,OAAAjE,IAAA,GAAAkE,OAAA;QACA,IAAA9B,GAAA;QACA4B,OAAA,CAAAlJ,iBAAA,CAAA6E,OAAA,WAAAkB,CAAA;UACAuB,GAAA,CAAAzB,IAAA,CAAAE,CAAA,CAAAE,WAAA;QACA;QACAiD,OAAA,CAAAzF,KAAA,CAAAmE,KAAA,CAAAJ,cAAA,CAAAF,GAAA;QACA4B,OAAA,CAAAzF,KAAA,CAAAmE,KAAA,CAAAyB,aAAA,GAAAH,OAAA,CAAAlJ,iBAAA;QACA;MACA;IACA;IACAsJ,QAAA,WAAAA,SAAA;MAAA,IAAAC,OAAA;MACA,KAAA5I,iBAAA;MAEAqI,UAAA;QACA,IAAA1B,GAAA;QACAiC,OAAA,CAAA9I,iBAAA,CAAAoE,OAAA,WAAAkB,CAAA;UACAuB,GAAA,CAAAzB,IAAA,CAAAE,CAAA,CAAAvG,EAAA;QACA;QACA+J,OAAA,CAAA9F,KAAA,CAAAC,IAAA,CAAA8D,cAAA,CAAAF,GAAA;MACA;IACA;IACAkC,WAAA,WAAAA,YAAAzD,CAAA;MAAA,IAAA0D,OAAA;MACA,KAAApG,UAAA;MACA2F,UAAA;QACAS,OAAA,CAAAhG,KAAA,CAAAvF,QAAA,CAAAoE,UAAA,CAAApB,MAAA,GAAA6E,CAAA,CAAAvG,EAAA;QACAiK,OAAA,CAAAhG,KAAA,CAAAvF,QAAA,CAAAwL,eAAA;MACA;IACA;IACAC,aAAA,WAAAA,cAAA5D,CAAA;MAAA,IAAA6D,OAAA;MACA,KAAAxG,QAAA,CAAAD,WAAA;MACA4C,CAAA,IACAA,CAAA,CAAAlB,OAAA,WAAA0C,OAAA;QACAqC,OAAA,CAAAxG,QAAA,CAAAD,WAAA,CAAA0C,IAAA,CAAA0B,OAAA,CAAA/H,EAAA;MACA;IACA;IACAqK,QAAA,WAAAA,SAAA;MAAA,IAAAC,OAAA;MACA;MACA,UAAA1G,QAAA,CAAAD,WAAA,CAAA4B,MAAA;QACA,KAAAwB,QAAA,CAAAwD,IAAA;MACA;QACA,KAAAtE,IAAA,kCAAArC,QAAA,EAAAqB,IAAA,WAAAvF,IAAA;UACA4K,OAAA,CAAAvD,QAAA;YACAzE,OAAA;YACA0C,IAAA;UACA;UACAsF,OAAA,CAAA1H,UAAA;QACA;MACA;IACA;IACA4H,kBAAA,WAAAA,mBAAA;MAAA,IAAAC,OAAA;MACA,KAAAxE,IAAA;QAAAjG,EAAA,OAAA4D,QAAA,CAAAlC;MAAA,GAAAuD,IAAA,CACA,UAAAvF,IAAA;QACA+K,OAAA,CAAA9G,WAAA;QACA8G,OAAA,CAAA7G,QAAA,CAAAD,WAAA;QACAjE,IAAA,CAAA2F,OAAA,WAAAkB,CAAA;UACAkE,OAAA,CAAA9G,WAAA,CAAA0C,IAAA,CAAAqE,MAAA,CAAAnE,CAAA;UACAkE,OAAA,CAAA7G,QAAA,CAAAD,WAAA,CAAA0C,IAAA,CAAAqE,MAAA,CAAAnE,CAAA;QACA;QACAkE,OAAA,CAAAxI,aAAA;QACAwI,OAAA,CAAAE,SAAA;UACAF,OAAA,CAAAxG,KAAA,CAAA2G,KAAA,CAAAC,OAAA,CAAAJ,OAAA,CAAA9G,WAAA;UACA6F,UAAA;YACAiB,OAAA,CAAAxI,aAAA;UACA;QACA;MACA,CACA;IACA;IACA6I,QAAA,WAAAA,SAAAvE,CAAA;MAAA,IAAAwE,OAAA;MACA,KAAA9E,IAAA;QAAAvE,MAAA,EAAA6E,CAAA,CAAAvG;MAAA,GAAAiF,IAAA,WAAAvF,IAAA;QACAqL,OAAA,CAAApL,gBAAA;QACAoL,OAAA,CAAAjJ,UAAA;QACA,IAAApC,IAAA;UACA,IACAA,IAAA,CAAA6B,kBAAA,uBACA7B,IAAA,CAAA6B,kBAAA,uBACA7B,IAAA,CAAA6B,kBAAA,qBACA;YACAwJ,OAAA,CAAA3J,QAAA,CAAAE,cAAA;YACAyJ,OAAA,CAAA3J,QAAA,CAAAI,mBAAA,GAAA9B,IAAA,CAAA6B,kBAAA;YACAwJ,OAAA,CAAA3J,QAAA,CAAAG,kBAAA,GAAA7B,IAAA,CAAA6B,kBAAA;YACAwJ,OAAA,CAAA3J,QAAA,CAAAK,MAAA;YACAsJ,OAAA,CAAA9J,iBAAA;YACA8J,OAAA,CAAAlK,iBAAA;YACAkK,OAAA,CAAAvK,iBAAA;YACAd,IAAA,CAAAsL,UAAA,CAAA3F,OAAA,WAAAkB,CAAA;cACA,IAAA7G,IAAA,CAAA6B,kBAAA;gBACAwJ,OAAA,CAAAvK,iBAAA,CAAA6F,IAAA;kBACAI,WAAA,WAAAF,CAAA,CAAAvG,EAAA;kBACAM,KAAA,EAAAiG,CAAA,CAAAzH;gBACA;cACA,WAAAY,IAAA,CAAA6B,kBAAA;gBACAwJ,OAAA,CAAA9J,iBAAA,CAAAoF,IAAA;kBACA+C,gBAAA,EAAA7C,CAAA,CAAAzH,IAAA;kBACAkB,EAAA,EAAAuG,CAAA,CAAAvG;gBACA;cACA,WAAAN,IAAA,CAAA6B,kBAAA;gBACAwJ,OAAA,CAAAlK,iBAAA,CAAAwF,IAAA;kBAAArG,EAAA,EAAAuG,CAAA,CAAAvG,EAAA;kBAAAb,QAAA,EAAAoH,CAAA,CAAAzH;gBAAA;cACA;cACAiM,OAAA,CAAA3J,QAAA,CAAAK,MAAA,CAAA4E,IAAA,CAAAE,CAAA,CAAAvG,EAAA;YACA;UACA;YACA+K,OAAA,CAAA3J,QAAA,CAAAE,cAAA,GAAA5B,IAAA,CAAA6B,kBAAA;YACAwJ,OAAA,CAAA3J,QAAA,CAAAG,kBAAA,GAAA7B,IAAA,CAAA6B,kBAAA;YACAwJ,OAAA,CAAA3J,QAAA,CAAAK,MAAA;UACA;UACAsJ,OAAA,CAAA3J,QAAA,CAAAC,WAAA,GAAA3B,IAAA,CAAA2B,WAAA,GAAA3B,IAAA,CAAA2B,WAAA;QACA;UACA0J,OAAA,CAAA9J,iBAAA;UACA8J,OAAA,CAAAlK,iBAAA;UACAkK,OAAA,CAAAvK,iBAAA;UACAuK,OAAA,CAAA3J,QAAA;YACAC,WAAA;YACAC,cAAA;YACAC,kBAAA;YACAC,mBAAA;YACAC,MAAA;YACAC,MAAA;UACA;QACA;QACAqJ,OAAA,CAAA3J,QAAA,CAAAM,MAAA,GAAA6E,CAAA,CAAAvG,EAAA;MACA;IACA;IACAiL,OAAA,WAAAA,QAAA1E,CAAA;MACA,KAAAxE,SAAA;MACA,KAAAH,OAAA,CAAAF,MAAA,GAAA6E,CAAA,CAAAvG,EAAA;MACA,KAAAoH,WAAA,CAAAb,CAAA,CAAAvG,EAAA;IACA;IACAkL,OAAA,WAAAA,QAAA3E,CAAA;MACA,KAAA3D,UAAA;MACA,KAAAgB,QAAA,CAAAlC,MAAA,GAAA6E,CAAA,CAAAvG,EAAA;MACA,KAAAmL,QAAA;MACA,KAAAX,kBAAA;IACA;IACAY,SAAA,WAAAA,UAAA;MACA,KAAAzI,MAAA;MACA,KAAAD,UAAA;MACA,KAAAR,QAAA,GAAA5D,CAAA,CAAA6D,SAAA,CAAAnD,WAAA;MACA,KAAAK,UAAA;IACA;IACAgM,UAAA,WAAAA,WAAA3L,IAAA;MACA,KAAAwC,QAAA,GAAAoJ,IAAA,CAAAC,KAAA,CAAAD,IAAA,CAAAE,SAAA,CAAA9L,IAAA;MACA,KAAAL,UAAA,KAAAK,IAAA,CAAAL,UAAA;MACA,KAAAsD,MAAA;MACA,KAAAD,UAAA;IACA;IACA+I,aAAA,WAAAA,cAAAC,GAAA,EAAAC,KAAA;MAAA,IAAAC,OAAA;MACA,IAAA9M,IAAA;MACA4M,GAAA,CAAArG,OAAA,WAAAkB,CAAA;QACA,IAAAA,CAAA,CAAAlG,QAAA;UACA,IAAAkG,CAAA,CAAAvG,EAAA,IAAA2L,KAAA;YACA7M,IAAA,GAAAyH,CAAA,CAAApH,QAAA;UACA;UACAyM,OAAA,CAAAH,aAAA,CAAAlF,CAAA,CAAAlG,QAAA,EAAAsL,KAAA;QACA;UACA,IAAApF,CAAA,CAAAvG,EAAA,IAAA2L,KAAA;YACA7M,IAAA,GAAAyH,CAAA,CAAApH,QAAA;UACA;QACA;MACA;MACA,OAAAL,IAAA;IACA;IACA+M,YAAA,WAAAA,aAAAtF,CAAA;MAAA,IAAAuF,OAAA;MACA,KAAAjH,QAAA;QACAC,iBAAA;QACAC,gBAAA;QACAC,IAAA;MACA,GACAC,IAAA;QACA6G,OAAA,CAAA7F,IAAA;UAAAjG,EAAA,EAAAuG;QAAA,GAAAtB,IAAA,WAAAvF,IAAA;UACAoM,OAAA,CAAA7H,KAAA,CAAA8H,IAAA,CAAAC,KAAA;UACAF,OAAA,CAAA/E,QAAA;YACAzE,OAAA;YACA0C,IAAA;UACA;QACA;MACA,GACAe,KAAA;IACA;IACAkG,UAAA,WAAAA,WAAAC,QAAA;MAAA,IAAAC,OAAA;MACA,KAAAlI,KAAA,CAAAiI,QAAA,EAAAE,QAAA,WAAAC,KAAA;QACA,IAAAA,KAAA;UACAF,OAAA,CAAAtM,OAAA;UACAsM,OAAA,CAAAlG,IAAA,oBAAAkG,OAAA,CAAAjK,QAAA,EAAA+C,IAAA,WAAAvF,IAAA;YACAyM,OAAA,CAAAxJ,MAAA;YACAwJ,OAAA,CAAAtM,OAAA;YACAsM,OAAA,CAAApF,QAAA;cACA/B,IAAA;cACA1C,OAAA;YACA;YACA6J,OAAA,CAAAlI,KAAA,CAAA8H,IAAA,CAAAC,KAAA;UACA;QACA;UACA;QACA;MACA;IACA;IACAM,WAAA,WAAAA,YAAA;MACA,KAAApK,QAAA,GAAA5D,CAAA,CAAA6D,SAAA,CAAAnD,WAAA;MACA,KAAAiF,KAAA,CAAA/B,QAAA,CAAAqK,WAAA;MACA,KAAA5J,MAAA;IACA;IACA6J,SAAA,WAAAA,UAAAN,QAAA;MACA,KAAAjI,KAAA,CAAAiI,QAAA,EAAAK,WAAA;IACA;IACAE,WAAA,WAAAA,YAAA;MACA,KAAAxI,KAAA,CAAA8H,IAAA,CAAAC,KAAA;IACA;IACAU,UAAA,WAAAA,WAAA;MAAA,IAAAC,OAAA;MACA,KAAA7J,UAAA,GAAAxE,CAAA,CAAA6D,SAAA,CAAAtD,iBAAA;MACA,KAAA8L,SAAA;QACAgC,OAAA,CAAA1I,KAAA,CAAA8H,IAAA,CAAAC,KAAA;MACA;IACA;IACAY,SAAA,WAAAA,UAAArG,CAAA;MACA,KAAAtD,OAAA,GAAAsD,CAAA;IACA;IACAsG,QAAA,WAAAA,SAAAtG,CAAA;MACA,KAAA/C,SAAA,GAAA+C,CAAA;IACA;IACAuG,WAAA,WAAAA,YAAA;MAAA,IAAAC,OAAA;MACA,SAAA/J,YAAA;QACA,KAAAiD,IAAA;UACAjD,YAAA,OAAAA;QACA,GAAAiC,IAAA,WAAAvF,IAAA;UACAqN,OAAA,CAAArJ,QAAA,GAAAhE,IAAA;QACA;MACA;QACA,KAAAoD,UAAA,CAAAwF,QAAA;QACA,KAAA6C,QAAA;MACA;IACA;IACAA,QAAA,WAAAA,SAAA;MAAA,IAAA6B,OAAA;MACA,KAAA/G,IAAA;QAAAqC,QAAA;MAAA,GAAArD,IAAA,WAAAvF,IAAA;QACAsN,OAAA,CAAAtJ,QAAA,GAAAhE,IAAA;MACA;IACA;EAAA;AAEA", "ignoreList": []}]}