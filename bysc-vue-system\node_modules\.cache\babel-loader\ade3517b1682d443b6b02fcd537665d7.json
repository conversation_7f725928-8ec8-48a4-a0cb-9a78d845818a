{"remainingRequest": "D:\\bw\\idcardbox-vue\\bysc-vue-system\\node_modules\\babel-loader\\lib\\index.js!D:\\bw\\idcardbox-vue\\bysc-vue-system\\src\\bysc_system\\service\\api\\account\\terminal.js", "dependencies": [{"path": "D:\\bw\\idcardbox-vue\\bysc-vue-system\\src\\bysc_system\\service\\api\\account\\terminal.js", "mtime": *************}, {"path": "D:\\bw\\idcardbox-vue\\bysc-vue-system\\babel.config.js", "mtime": *************}, {"path": "D:\\bw\\idcardbox-vue\\bysc-vue-system\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": *************}, {"path": "D:\\bw\\idcardbox-vue\\bysc-vue-system\\node_modules\\babel-loader\\lib\\index.js", "mtime": *************}], "contextDependencies": [], "result": ["export default [{\n  name: 'terminal-page',\n  // 终端分页\n  method: 'POST',\n  path: '/icb/deviceIcbZh/page'\n}, {\n  name: 'terminal-save',\n  // 保存\n  method: 'POST',\n  path: '/icb/deviceIcbZh/save'\n}, {\n  name: 'terminal-delete',\n  // 删除\n  method: 'POST',\n  path: '/icb/deviceIcbZh/delete'\n}, {\n  name: 'terminal-delete',\n  // 删除\n  method: 'POST',\n  path: '/icb//deviceIcbZh/changeTenantInfo'\n}];", {"version": 3, "names": ["name", "method", "path"], "sources": ["D:/bw/idcardbox-vue/bysc-vue-system/src/bysc_system/service/api/account/terminal.js"], "sourcesContent": ["export default [\r\n  {\r\n    name: 'terminal-page', // 终端分页\r\n    method: 'POST',\r\n    path: '/icb/deviceIcbZh/page'\r\n  },\r\n  {\r\n    name: 'terminal-save', // 保存\r\n    method: 'POST',\r\n    path: '/icb/deviceIcbZh/save'\r\n  },\r\n  {\r\n    name: 'terminal-delete', // 删除\r\n    method: 'POST',\r\n    path: '/icb/deviceIcbZh/delete'\r\n  },\r\n  {\r\n    name: 'terminal-delete', // 删除\r\n    method: 'POST',\r\n    path: '/icb//deviceIcbZh/changeTenantInfo'\r\n  }\r\n];\r\n"], "mappings": "AAAA,eAAe,CACb;EACEA,IAAI,EAAE,eAAe;EAAE;EACvBC,MAAM,EAAE,MAAM;EACdC,IAAI,EAAE;AACR,CAAC,EACD;EACEF,IAAI,EAAE,eAAe;EAAE;EACvBC,MAAM,EAAE,MAAM;EACdC,IAAI,EAAE;AACR,CAAC,EACD;EACEF,IAAI,EAAE,iBAAiB;EAAE;EACzBC,MAAM,EAAE,MAAM;EACdC,IAAI,EAAE;AACR,CAAC,EACD;EACEF,IAAI,EAAE,iBAAiB;EAAE;EACzBC,MAAM,EAAE,MAAM;EACdC,IAAI,EAAE;AACR,CAAC,CACF", "ignoreList": []}]}