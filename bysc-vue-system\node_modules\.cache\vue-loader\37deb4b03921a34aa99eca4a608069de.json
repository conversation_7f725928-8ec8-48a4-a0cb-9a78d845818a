{"remainingRequest": "D:\\bw\\idcardbox-vue\\bysc-vue-system\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\bw\\idcardbox-vue\\bysc-vue-system\\src\\bysc_system\\views\\role\\index.vue?vue&type=style&index=0&id=f7dbd3ca&lang=less&scoped=true", "dependencies": [{"path": "D:\\bw\\idcardbox-vue\\bysc-vue-system\\src\\bysc_system\\views\\role\\index.vue", "mtime": 1753952638725}, {"path": "D:\\bw\\idcardbox-vue\\bysc-vue-system\\node_modules\\css-loader\\index.js", "mtime": 1745221300128}, {"path": "D:\\bw\\idcardbox-vue\\bysc-vue-system\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1745221314654}, {"path": "D:\\bw\\idcardbox-vue\\bysc-vue-system\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1745221303798}, {"path": "D:\\bw\\idcardbox-vue\\bysc-vue-system\\node_modules\\less-loader\\dist\\cjs.js", "mtime": 1745221307121}, {"path": "D:\\bw\\idcardbox-vue\\bysc-vue-system\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745221302709}, {"path": "D:\\bw\\idcardbox-vue\\bysc-vue-system\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1745221307761}], "contextDependencies": [], "result": ["\r\n.demo-drawer-footer {\r\n  width: 100%;\r\n  position: absolute;\r\n  bottom: 0;\r\n  left: 0;\r\n  border-top: 1px solid #e8e8e8;\r\n  padding: 10px 16px;\r\n  text-align: right;\r\n  background: #fff;\r\n  z-index: 100;\r\n}\r\n.el-dropdown-link {\r\n  cursor: pointer;\r\n  color: #409eff;\r\n}\r\n.el-icon-arrow-down {\r\n  font-size: 12px;\r\n}\r\n.appCard {\r\n  width: 100%;\r\n  overflow: hidden;\r\n  height: 60px;\r\n  margin-bottom: 10px;\r\n  position: relative;\r\n  border: 1px solid #ccc;\r\n  border-radius: 5px;\r\n}\r\n.cardTitle {\r\n  position: absolute;\r\n  z-index: 999;\r\n  width: 150px;\r\n  text-align: center;\r\n  font-weight: bold;\r\n  height: 60px;\r\n  line-height: 60px;\r\n  top: 0px;\r\n  left: 0;\r\n}\r\n", {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";AAw0CA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/bysc_system/views/role", "sourcesContent": ["<!--\r\n * @Author: czw\r\n * @Date: 2022-11-03 17:55:45\r\n * @LastEditors: czw\r\n * @LastEditTime: 2022-12-09 09:26:18\r\n * @FilePath: \\bycloud-vue\\src\\bysc_system\\views\\role\\index.vue\r\n * @Description:\r\n *\r\n * Copyright (c) 2022 by czw/bysc, All Rights Reserved.\r\n-->\r\n<!--  -->\r\n<template>\r\n  <div>\r\n    <el-row>\r\n      <el-col :span=\"24\">\r\n        <Grid\r\n          api=\"systems/rolePage\"\r\n          :event-bus=\"searchEventBus\"\r\n          :search-params=\"searchForm\"\r\n          :newcolumn=\"columns\"\r\n          @datas=\"getDatas\"\r\n          @columnChange=\"getcolumn\"\r\n          ref=\"grid\"\r\n        >\r\n          <div slot=\"search\">\r\n            <el-input\r\n              style=\"width: 200px; margin: 0 10px 0 0\"\r\n              v-model.trim=\"searchForm.name\"\r\n              size=\"small\"\r\n              placeholder=\"请输入名称\"\r\n            ></el-input>\r\n            <el-input\r\n              style=\"width: 200px; margin: 0 10px 0 0\"\r\n              v-model.trim=\"searchForm.code\"\r\n              size=\"small\"\r\n              placeholder=\"请输入标识\"\r\n            ></el-input>\r\n            <el-button\r\n              size=\"small\"\r\n              type=\"primary\"\r\n              style=\"margin: 0 0 0 10px\"\r\n              @click=\"searchTable\"\r\n              >搜索</el-button\r\n            >\r\n            <el-button size=\"small\" @click=\"resetTable\">重置</el-button>\r\n          </div>\r\n          <div slot=\"action\">\r\n            <el-button size=\"small\" type=\"primary\" @click=\"handleAdd\"\r\n              >添加</el-button\r\n            >\r\n            <el-button size=\"small\" type=\"primary\" @click=\"goExport\"\r\n              >导出</el-button\r\n            >\r\n          </div>\r\n          <el-table\r\n            slot=\"table\"\r\n            slot-scope=\"{ loading }\"\r\n            v-loading=\"loading\"\r\n            :data=\"tableData\"\r\n            stripe\r\n            style=\"width: 100%\"\r\n          >\r\n            <el-table-column\r\n              fixed=\"left\"\r\n              :align=\"'center'\"\r\n              type=\"selection\"\r\n              width=\"55\"\r\n            >\r\n            </el-table-column>\r\n            <el-table-column\r\n              fixed=\"left\"\r\n              :align=\"'center'\"\r\n              label=\"序号\"\r\n              type=\"index\"\r\n              width=\"50\"\r\n            >\r\n            </el-table-column>\r\n            <template v-for=\"(item, index) in columns\">\r\n              <el-table-column\r\n                v-if=\"item.slot === 'roleStatus'\"\r\n                :show-overflow-tooltip=\"true\"\r\n                :align=\"item.align ? item.align : 'center'\"\r\n                :key=\"index\"\r\n                :prop=\"item.key\"\r\n                :label=\"item.title\"\r\n                min-width=\"180\"\r\n              >\r\n                <template slot-scope=\"scope\">\r\n                  {{ scope.row[item.slot] ? \"启用\" : \"禁用\" }}\r\n                </template>\r\n              </el-table-column>\r\n              <el-table-column\r\n                v-else-if=\"item.slot === 'roleType'\"\r\n                :show-overflow-tooltip=\"true\"\r\n                :align=\"item.align ? item.align : 'center'\"\r\n                :key=\"index\"\r\n                :prop=\"item.key\"\r\n                :label=\"item.title\"\r\n                min-width=\"180\"\r\n              >\r\n                <template slot-scope=\"scope\">\r\n                  {{ scope.row[item.slot] === 1 ? '超管' :\r\n                     scope.row[item.slot] === 2 ? '管理员' :\r\n                     scope.row[item.slot] === 3 ? '普通用户' : '' }}\r\n                </template>\r\n              </el-table-column>\r\n              <el-table-column\r\n                v-else\r\n                :show-overflow-tooltip=\"true\"\r\n                :key=\"item.key\"\r\n                :prop=\"item.key\"\r\n                :label=\"item.title\"\r\n                :min-width=\"item.width ? item.width : '150'\"\r\n                :align=\"item.align ? item.align : 'center'\"\r\n              >\r\n              </el-table-column>\r\n            </template>\r\n            <el-table-column\r\n              fixed=\"right\"\r\n              align=\"center\"\r\n              label=\"操作\"\r\n              type=\"action\"\r\n              width=\"180\"\r\n            >\r\n              <template slot-scope=\"scope\">\r\n                <el-button @click=\"getRoleUser(scope.row)\" type=\"text\"\r\n                  >角色用户</el-button\r\n                >\r\n                <el-dropdown style=\"margin-left: 6px\">\r\n                  <span class=\"el-dropdown-link\" style=\"font-size: 14px\">\r\n                    更多<i class=\"el-icon-arrow-down el-icon--right\"></i>\r\n                  </span>\r\n                  <el-dropdown-menu slot=\"dropdown\">\r\n                    <el-dropdown-item @click.native=\"handleRdit(scope.row)\">\r\n                      <el-button type=\"text\">编辑</el-button>\r\n                    </el-dropdown-item>\r\n                    <el-dropdown-item @click.native=\"setMenu(scope.row)\">\r\n                      <el-button type=\"text\">资源配置</el-button>\r\n                    </el-dropdown-item>\r\n                    <el-dropdown-item @click.native=\"setDatas(scope.row)\">\r\n                      <el-button type=\"text\">数据配置</el-button>\r\n                    </el-dropdown-item>\r\n                    <el-dropdown-item @click.native=\"setApps(scope.row)\">\r\n                      <el-button type=\"text\">应用配置</el-button>\r\n                    </el-dropdown-item>\r\n                    <el-dropdown-item\r\n                      v-if=\"scope.row.canDelete\"\r\n                      @click.native=\"handleDelete(scope.row.id)\"\r\n                    >\r\n                      <el-button type=\"text\">删除</el-button>\r\n                    </el-dropdown-item>\r\n                  </el-dropdown-menu>\r\n                </el-dropdown>\r\n              </template>\r\n            </el-table-column>\r\n          </el-table>\r\n        </Grid>\r\n      </el-col>\r\n    </el-row>\r\n    <el-drawer\r\n      size=\"50%\"\r\n      title=\"资源配置\"\r\n      :visible.sync=\"menuDrawer\"\r\n      :direction=\"direction\"\r\n    >\r\n      <div style=\"width: 100%; padding: 0 10px; margin-bottom: 100px\">\r\n        <checkTree\r\n          ref=\"ctree\"\r\n          :checkStrictly=\"checkStrictly\"\r\n          :defaultexpandall=\"true\"\r\n          :tree-props=\"treeProps\"\r\n          :tree-data=\"treedata\"\r\n          @treeNode=\"getSelectKeys\"\r\n        ></checkTree>\r\n        <div class=\"demo-drawer-footer\">\r\n          <el-button\r\n            size=\"small\"\r\n            type=\"primary\"\r\n            @click=\"saveMenu()\"\r\n            :loading=\"loading\"\r\n            >保存</el-button\r\n          >\r\n        </div>\r\n      </div>\r\n    </el-drawer>\r\n    <el-drawer\r\n      size=\"50%\"\r\n      :title=\"drawerName\"\r\n      :visible.sync=\"drawer\"\r\n      :direction=\"direction\"\r\n    >\r\n      <div style=\"width: 100%; padding: 0 10px\">\r\n        <el-form\r\n          :model=\"ruleForm\"\r\n          :rules=\"rules\"\r\n          ref=\"ruleForm\"\r\n          label-width=\"100px\"\r\n          class=\"demo-ruleForm\"\r\n        >\r\n          <el-form-item label=\"名称\" prop=\"roleName\">\r\n            <el-input\r\n              size=\"small\"\r\n              maxlength=\"15\"\r\n              v-model.trim=\"ruleForm.roleName\"\r\n            ></el-input>\r\n          </el-form-item>\r\n          <el-form-item label=\"标识\" prop=\"roleKey\">\r\n            <el-input\r\n              size=\"small\"\r\n              maxlength=\"32\"\r\n              v-model.trim=\"ruleForm.roleKey\"\r\n            ></el-input>\r\n          </el-form-item>\r\n\r\n          <el-form-item label=\"角色类型\" prop=\"roleType\">\r\n            <el-select\r\n              v-model=\"ruleForm.roleType\"\r\n              size=\"small\"\r\n              placeholder=\"请选择角色类型\"\r\n            >\r\n              <el-option label=\"超管\" :value=\"1\"></el-option>\r\n              <el-option label=\"管理员\" :value=\"2\"></el-option>\r\n              <el-option label=\"普通用户\" :value=\"3\"></el-option>\r\n            </el-select>\r\n          </el-form-item>\r\n\r\n          <el-form-item label=\"角色描述\" prop=\"comments\">\r\n            <el-input\r\n              type=\"textarea\"\r\n              size=\"small\"\r\n              maxlength=\"200\"\r\n              v-model.trim=\"ruleForm.comments\"\r\n            ></el-input>\r\n          </el-form-item>\r\n          <el-form-item label=\"是否启用\" prop=\"roleStatus\">\r\n            <el-switch v-model.trim=\"roleStatus\"></el-switch>\r\n          </el-form-item>\r\n          <div class=\"demo-drawer-footer\">\r\n            <el-button size=\"small\" @click=\"closeDrawer\">关闭</el-button>\r\n            <el-button\r\n              size=\"small\"\r\n              type=\"primary\"\r\n              @click=\"submitForm('ruleForm')\"\r\n              >保存</el-button\r\n            >\r\n          </div>\r\n        </el-form>\r\n      </div>\r\n    </el-drawer>\r\n    <el-drawer\r\n      size=\"700px\"\r\n      stitle=\"角色用户\"\r\n      :visible.sync=\"roleDrawer\"\r\n      :direction=\"direction\"\r\n    >\r\n      <div style=\"width: 100%; padding: 0 10px\">\r\n        <roleUser ref=\"roleUser\"></roleUser>\r\n      </div>\r\n    </el-drawer>\r\n    <el-drawer\r\n      size=\"700px\"\r\n      title=\"数据配置\"\r\n      :modal=\"false\"\r\n      :visible.sync=\"dataDrawer\"\r\n      :direction=\"direction\"\r\n    >\r\n      <div style=\"width: 100%; padding: 0 10px; position: relative\">\r\n        <!-- <span style=\"font-weight:bold\">数据配置：</span> -->\r\n        <el-radio-group v-model=\"dataForm.permissionType\">\r\n          <el-radio style=\"width: 100%; margin-top: 5px\" :label=\"'ME'\"\r\n            >本人</el-radio\r\n          >\r\n          <!-- <el-radio style=\"width: 100%; margin-top: 5px\" :label=\"'SUBORDINATE'\"\r\n            >本人及下属</el-radio\r\n          > -->\r\n          <!-- <el-radio\r\n            style=\"width: 100%; margin-top: 5px\"\r\n            :label=\"'MY_DEPARTMENT'\"\r\n            >本部门</el-radio\r\n          > -->\r\n          <!-- <el-radio\r\n            style=\"width: 100%; margin-top: 5px\"\r\n            :label=\"'MY_DEPARTMENT_AND_SUB'\"\r\n            >本部门及下属</el-radio\r\n          > -->\r\n          <el-radio style=\"width: 100%; margin-top: 5px\" :label=\"'ALL'\"\r\n            >全部</el-radio\r\n          >\r\n          <el-radio style=\"width: 100%; margin-top: 5px\" :label=\"8\"\r\n            >指定范围</el-radio\r\n          >\r\n        </el-radio-group>\r\n        <div style=\"margin-top: 10px\" v-show=\"dataForm.permissionType === 8\">\r\n          <span>指定范围：</span>\r\n          <el-radio-group v-model=\"dataForm.permissionTypeCode1\">\r\n            <el-radio :label=\"'SPECIFIC_USER'\">指定用户</el-radio>\r\n            <el-radio :label=\"'SPECIFIC_DEPT'\">指定部门/分子公司</el-radio>\r\n            <el-radio :label=\"'SPECIFIC_ROLE'\">指定组织角色</el-radio>\r\n          </el-radio-group>\r\n        </div>\r\n        <div\r\n          v-show=\"dataForm.permissionType === 8\"\r\n          style=\"width: 100%; height: 1px; background: #ccc; margin-top: 15px\"\r\n        ></div>\r\n        <div v-show=\"dataForm.permissionType === 8\" style=\"margin-top: 15px\">\r\n          <div\r\n            style=\"margin-top: 5px\"\r\n            v-show=\"dataForm.permissionTypeCode1 == 'SPECIFIC_USER'\"\r\n          >\r\n            <div style=\"font-weight: bold; margin-bottom: 8px\">指定用户</div>\r\n            <el-button\r\n              style=\"margin-right: 5px\"\r\n              @click=\"addUsers\"\r\n              type=\"primary\"\r\n              size=\"small\"\r\n              icon=\"el-icon-circle-plus-outline\"\r\n              >添加</el-button\r\n            >\r\n            <el-tag\r\n              style=\"margin: 5px\"\r\n              v-for=\"tag in selectedUserLists\"\r\n              :key=\"tag.id\"\r\n              closable\r\n              @close=\"handleUserClose(tag)\"\r\n            >\r\n              {{ tag.label }}\r\n            </el-tag>\r\n          </div>\r\n          <div\r\n            style=\"margin-top: 5px\"\r\n            v-show=\"dataForm.permissionTypeCode1 == 'SPECIFIC_DEPT'\"\r\n          >\r\n            <div style=\"font-weight: bold; margin-bottom: 8px\">指定部门</div>\r\n            <el-button\r\n              style=\"margin-right: 5px\"\r\n              @click=\"addDepts\"\r\n              type=\"primary\"\r\n              size=\"small\"\r\n              icon=\"el-icon-circle-plus-outline\"\r\n              >添加</el-button\r\n            >\r\n            <el-tag\r\n              style=\"margin: 5px\"\r\n              v-for=\"tag in selectedDeptLists\"\r\n              :key=\"tag.id\"\r\n              closable\r\n              @close=\"handleDeptClose(tag)\"\r\n            >\r\n              {{ tag.organizationName }}\r\n            </el-tag>\r\n          </div>\r\n          <div\r\n            style=\"margin-top: 5px\"\r\n            v-show=\"dataForm.permissionTypeCode1 == 'SPECIFIC_ROLE'\"\r\n          >\r\n            <div style=\"font-weight: bold; margin-bottom: 8px\">\r\n              指定组织角色\r\n            </div>\r\n            <el-button\r\n              style=\"margin-right: 5px\"\r\n              @click=\"addRoles\"\r\n              type=\"primary\"\r\n              size=\"small\"\r\n              icon=\"el-icon-circle-plus-outline\"\r\n              >添加</el-button\r\n            >\r\n            <el-tag\r\n              style=\"margin: 5px\"\r\n              v-for=\"tag in selectedRoleLists\"\r\n              :key=\"tag.id\"\r\n              closable\r\n              @close=\"handleClose(tag)\"\r\n            >\r\n              {{ tag.roleName }}\r\n            </el-tag>\r\n          </div>\r\n          <el-dialog\r\n            title=\"指定用户\"\r\n            :modal=\"false\"\r\n            :visible.sync=\"dialogUserVisible\"\r\n            width=\"50%\"\r\n          >\r\n            <div style=\"width: 100%; height: 500px; overflow: auto\">\r\n              <!-- <div style=\"width:100%\">\r\n                <el-input\r\n                  placeholder=\"输入关键字进行过滤\"\r\n                  size=\"small\"\r\n                  style=\"margin-bottom:10px;width:48%\"\r\n                  v-model=\"filterText\">\r\n                </el-input>\r\n              </div> -->\r\n              <div\r\n                style=\"\r\n                  height: 100%;\r\n                  width: 50%;\r\n                  float: left;\r\n                  padding: 10px;\r\n                  overflow-y: scroll;\r\n                \"\r\n              >\r\n                <Lazytrees ref=\"ltree\" @treeNode=\"getUserData\"></Lazytrees>\r\n                <!-- <el-tree\r\n                  :data=\"userLists\"\r\n                  show-checkbox\r\n                  :check-strictly=\"true\"\r\n                  @check=\"getUserTrees\"\r\n                  node-key=\"primaryCode\"\r\n                  ref=\"tree\"\r\n                  :default-expand-all=\"true\"\r\n                  :filter-node-method=\"filterNode\"\r\n                  :props=\"defaultUserProps\">\r\n                </el-tree> -->\r\n              </div>\r\n              <div\r\n                style=\"\r\n                  height: 100%;\r\n                  width: 50%;\r\n                  float: left;\r\n                  padding: 10px;\r\n                  border: 1px solid #f2f2f2;\r\n                \"\r\n              >\r\n                <div\r\n                  style=\"\r\n                    text-align: center;\r\n                    height: 30px;\r\n                    width: 100%;\r\n                    line-height: 30px;\r\n                  \"\r\n                >\r\n                  已选择的用户\r\n                </div>\r\n                <div>\r\n                  <el-tag\r\n                    style=\"margin: 5px\"\r\n                    v-for=\"tag in selectedUserLists\"\r\n                    :key=\"tag.primaryCode\"\r\n                    closable\r\n                    @close=\"handleUserClose(tag)\"\r\n                  >\r\n                    {{ tag.label }}\r\n                  </el-tag>\r\n                </div>\r\n              </div>\r\n            </div>\r\n            <span slot=\"footer\" class=\"dialog-footer\">\r\n              <el-button\r\n                size=\"small\"\r\n                type=\"primary\"\r\n                @click=\"dialogUserVisible = false\"\r\n                >确 定</el-button\r\n              >\r\n            </span>\r\n          </el-dialog>\r\n          <el-dialog\r\n            title=\"选择部门\"\r\n            :modal=\"false\"\r\n            :visible.sync=\"dialogDeptVisible\"\r\n            width=\"50%\"\r\n          >\r\n            <div style=\"width: 100%; height: 500px; overflow: auto\">\r\n              <div style=\"height: 100%; width: 50%; float: left; padding: 10px\">\r\n                <el-input\r\n                  placeholder=\"输入关键字进行过滤\"\r\n                  size=\"small\"\r\n                  style=\"margin-bottom: 10px; width: 90%\"\r\n                  v-model=\"filterText\"\r\n                >\r\n                </el-input>\r\n                <el-tree\r\n                  :data=\"DeptLists\"\r\n                  show-checkbox\r\n                  :check-strictly=\"false\"\r\n                  @check=\"getDeptTrees\"\r\n                  node-key=\"id\"\r\n                  ref=\"tree\"\r\n                  :filter-node-method=\"filterDeptNode\"\r\n                  :props=\"defaultDeptProps\"\r\n                >\r\n                </el-tree>\r\n              </div>\r\n              <div\r\n                style=\"\r\n                  height: 100%;\r\n                  width: 50%;\r\n                  float: left;\r\n                  padding: 10px;\r\n                  border: 1px solid #f2f2f2;\r\n                \"\r\n              >\r\n                <div\r\n                  style=\"\r\n                    text-align: center;\r\n                    height: 30px;\r\n                    width: 100%;\r\n                    line-height: 30px;\r\n                  \"\r\n                >\r\n                  已选择的部门\r\n                </div>\r\n                <div>\r\n                  <el-tag\r\n                    style=\"margin: 5px\"\r\n                    v-for=\"tag in selectedDeptLists\"\r\n                    :key=\"tag.id\"\r\n                    closable\r\n                    @close=\"handleDeptClose(tag)\"\r\n                  >\r\n                    {{ tag.organizationName }}\r\n                  </el-tag>\r\n                </div>\r\n              </div>\r\n            </div>\r\n            <span slot=\"footer\" class=\"dialog-footer\">\r\n              <el-button\r\n                size=\"small\"\r\n                type=\"primary\"\r\n                @click=\"\r\n                  dialogDeptVisible = false;\r\n                  updateDeptRefIds();\r\n                \"\r\n                >确 定</el-button\r\n              >\r\n            </span>\r\n          </el-dialog>\r\n          <el-dialog\r\n            title=\"组织选择角色\"\r\n            :modal=\"false\"\r\n            :visible.sync=\"dialogRoleVisible\"\r\n            width=\"50%\"\r\n          >\r\n            <div style=\"width: 100%; height: 500px; overflow: auto\">\r\n              <div style=\"height: 100%; width: 50%; float: left; padding: 10px\">\r\n                <el-input\r\n                  placeholder=\"输入关键字进行过滤\"\r\n                  size=\"small\"\r\n                  style=\"margin-bottom: 10px; width: 90%\"\r\n                  v-model=\"filterText\"\r\n                >\r\n                </el-input>\r\n                <el-tree\r\n                  :data=\"roleLists\"\r\n                  show-checkbox\r\n                  @check=\"getRoleTrees\"\r\n                  node-key=\"id\"\r\n                  ref=\"tree\"\r\n                  :filter-node-method=\"filterNode\"\r\n                  :props=\"defaultProps\"\r\n                >\r\n                </el-tree>\r\n              </div>\r\n              <div\r\n                style=\"\r\n                  height: 100%;\r\n                  width: 50%;\r\n                  float: left;\r\n                  padding: 10px;\r\n                  border: 1px solid #f2f2f2;\r\n                \"\r\n              >\r\n                <div\r\n                  style=\"\r\n                    text-align: center;\r\n                    height: 30px;\r\n                    width: 100%;\r\n                    line-height: 30px;\r\n                  \"\r\n                >\r\n                  已选择的组织角色\r\n                </div>\r\n                <div>\r\n                  <el-tag\r\n                    style=\"margin: 5px\"\r\n                    v-for=\"tag in selectedRoleLists\"\r\n                    :key=\"tag.id\"\r\n                    closable\r\n                    @close=\"handleClose(tag)\"\r\n                  >\r\n                    {{ tag.roleName }}\r\n                  </el-tag>\r\n                </div>\r\n              </div>\r\n            </div>\r\n            <span slot=\"footer\" class=\"dialog-footer\">\r\n              <el-button\r\n                size=\"small\"\r\n                type=\"primary\"\r\n                @click=\"dialogRoleVisible = false\"\r\n                >确 定</el-button\r\n              >\r\n            </span>\r\n          </el-dialog>\r\n        </div>\r\n        <div style=\"padding-top: 20px\">\r\n          <el-divider content-position=\"left\">生效模块</el-divider>\r\n          <el-checkbox-group v-model=\"dataForm.moduleNames\" size=\"small\">\r\n            <div\r\n              style=\"margin-top: 10px\"\r\n              v-for=\"item in moduleNameLists\"\r\n              :key=\"item.id\"\r\n            >\r\n              <el-checkbox :label=\"item.dictCode\" border>{{\r\n                item.dictName\r\n              }}</el-checkbox>\r\n            </div>\r\n          </el-checkbox-group>\r\n        </div>\r\n\r\n        <div style=\"width: 100%; position: absolute; right: 20px; top: 85vh\">\r\n          <el-button\r\n            style=\"float: right\"\r\n            size=\"small\"\r\n            type=\"primary\"\r\n            @click=\"submitData\"\r\n            :loading=\"loading\"\r\n            >{{ loading ? \"提交中 ...\" : \"确 定\" }}</el-button\r\n          >\r\n        </div>\r\n      </div>\r\n    </el-drawer>\r\n    <!-- 应用配置 -->\r\n    <el-drawer\r\n      size=\"700px\"\r\n      title=\"应用配置\"\r\n      :visible.sync=\"appDrawer\"\r\n      :direction=\"direction\"\r\n    >\r\n      <div style=\"width: 100%; padding: 0 10px\">\r\n        <el-checkbox-group v-model=\"appForm.appIds\">\r\n          <el-row :gutter=\"20\">\r\n            <el-col\r\n              :span=\"6\"\r\n              v-for=\"(item, index) in appLists\"\r\n              :key=\"index + 'app'\"\r\n            >\r\n              <div class=\"appCard\">\r\n                <el-checkbox\r\n                  style=\"position: absolute; left: 3px\"\r\n                  :key=\"item.appId\"\r\n                  :label=\"item.appId\"\r\n                >\r\n                  <div class=\"cardTitle\">{{ item.appName }}</div>\r\n                </el-checkbox>\r\n              </div>\r\n            </el-col>\r\n          </el-row>\r\n        </el-checkbox-group>\r\n        <div>\r\n          <el-button\r\n            style=\"float: right; margin-top: 60px\"\r\n            @click=\"saveAppLists()\"\r\n            type=\"primary\"\r\n            size=\"small\"\r\n          >\r\n            保存\r\n          </el-button>\r\n        </div>\r\n      </div>\r\n    </el-drawer>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport Vue from \"vue\";\r\nimport {mapActions} from \"vuex\";\r\nimport Grid from \"@/components/Grid\";\r\nimport _ from \"lodash\";\r\nimport commonTree from \"@/components/treeComp/commonTree\";\r\nimport checkTree from \"@/components/treeComp/checkTree\";\r\nimport iconChoose from \"@/components/choose/icon-choose\";\r\nimport roleUser from \"@/bysc_system/views/role/roleUser\";\r\nimport Lazytrees from \"@/components/treeComp/ltrees.vue\";\r\nimport dayjs from \"dayjs\";\r\nconst defaultSearchForm = {\r\n  name: \"\",\r\n  code: \"\",\r\n};\r\nconst defaultForm = {\r\n  comments: \"\",\r\n  roleKey: \"\",\r\n  roleName: \"\",\r\n  roleType: \"\",\r\n  roleStatus: 1,\r\n};\r\nexport default {\r\n  components: {commonTree, Grid, iconChoose, checkTree, roleUser, Lazytrees},\r\n  destroyed() {\r\n    this.searchEventBus.$off();\r\n  },\r\n  data() {\r\n    this.searchEventBus = new Vue();\r\n    return {\r\n      selectModuleName: [],\r\n      moduleNameLists: [],\r\n      loading: false,\r\n      selectedAppList: [],\r\n      appList: [\r\n        {\r\n          name: \"工作流\",\r\n          id: 1,\r\n        },\r\n        {\r\n          name: \"系统\",\r\n          id: 2,\r\n        },\r\n        {\r\n          name: \"消息模块\",\r\n          id: 3,\r\n        },\r\n        {\r\n          name: \"库存\",\r\n          id: 4,\r\n        },\r\n        {\r\n          name: \"采购\",\r\n          id: 5,\r\n        },\r\n        {\r\n          name: \"质量\",\r\n          id: 6,\r\n        },\r\n        {\r\n          name: \"运行\",\r\n          id: 7,\r\n        },\r\n      ],\r\n      bgurls: [\r\n        {url: require(\"./images/bg1.png\")},\r\n        {url: require(\"./images/bg2.png\")},\r\n        {url: require(\"./images/bg3.png\")},\r\n      ],\r\n      defaultUserProps: {\r\n        children: \"children\",\r\n        label: \"label\",\r\n        roles: [],\r\n      },\r\n      selectedUserLists: [],\r\n      userLists: [],\r\n      dialogUserVisible: false,\r\n      filterText: \"\",\r\n      defaultProps: {\r\n        children: \"children\",\r\n        label: \"roleName\",\r\n        roles: [],\r\n      },\r\n      selectedRoleLists: [],\r\n      roleLists: [],\r\n      dialogRoleVisible: false,\r\n      defaultDeptProps: {\r\n        children: \"children\",\r\n        label: \"organizationName\",\r\n        roles: [],\r\n      },\r\n      selectedDeptLists: [],\r\n      DeptLists: [],\r\n      dialogDeptVisible: false,\r\n      dataForm: {\r\n        moduleNames: [],\r\n        permissionType: \"\",\r\n        permissionTypeCode: \"\",\r\n        permissionTypeCode1: \"\",\r\n        refIds: [],\r\n        roleId: null,\r\n        allUserDeptIds: [],\r\n      },\r\n      appForm: {\r\n        appIds: [],\r\n        roleId: null,\r\n      },\r\n      dataDrawer: false,\r\n      appDrawer: false,\r\n      dialogVisible: false,\r\n      roleStatus: true,\r\n      checkStrictly: true,\r\n      ruleForm: _.cloneDeep(defaultForm),\r\n      rules: {\r\n        roleName: [\r\n          {required: true, message: \"请输入名称\", trigger: \"blur\"},\r\n          {\r\n            min: 1,\r\n            max: 15,\r\n            message: \"长度在 1 到 15 个字符\",\r\n            trigger: \"blur\",\r\n          },\r\n        ],\r\n        roleKey: [{required: true, message: \"请输入标识\", trigger: \"blur\"}],\r\n        roleType: [\r\n          {required: true, message: \"请选择角色类型\", trigger: \"change\"},\r\n        ],\r\n      },\r\n      drawerName: \"添加\",\r\n      drawer: false,\r\n      menuDrawer: false,\r\n      direction: \"rtl\",\r\n      searchForm: _.cloneDeep(defaultSearchForm),\r\n      treeProps: {\r\n        children: \"children\",\r\n        label: \"resourceName\",\r\n      },\r\n      resourceName: \"\",\r\n      columns: [\r\n        {\r\n          title: \"名称\",\r\n          key: \"roleName\",\r\n          tooltip: true,\r\n          minWidth: 130,\r\n        },\r\n        {\r\n          title: \"标识\",\r\n          key: \"roleKey\",\r\n          minWidth: 150,\r\n          tooltip: true,\r\n        },\r\n        {\r\n          title: \"角色状态\",\r\n          slot: \"roleStatus\",\r\n          tooltip: true,\r\n          minWidth: 170,\r\n        },\r\n\r\n        {\r\n          title: \"角色类型\",\r\n          slot: \"roleType\",\r\n          tooltip: true,\r\n          minWidth: 170,\r\n        },\r\n        {\r\n          title: \"角色描述\",\r\n          key: \"comments\",\r\n          tooltip: true,\r\n          minWidth: 170,\r\n        },\r\n      ],\r\n      permissionsList: [],\r\n      roleName: \"\",\r\n      tableData: [],\r\n      selectedResouce: {},\r\n      treedata: [],\r\n      resourceIds: [],\r\n      roleForm: {\r\n        resourceIds: [],\r\n        roleId: \"\",\r\n      },\r\n      roleId: \"\",\r\n      roleDrawer: false,\r\n      appLists: [],\r\n    };\r\n  },\r\n  watch: {\r\n    roleStatus(val) {\r\n      this.ruleForm.roleStatus = val ? 1 : 0;\r\n    },\r\n    // 'dataForm.permissionTypeCode1'(val) {\r\n    //   if (val === 'SPECIFIC_USER') {\r\n    //     this.selectedUserLists = [];\r\n    //     this.selectedRoleLists = [];\r\n    //   } else if (val === 'SPECIFIC_DEPT') {\r\n    //     this.selectedRoleLists = [];\r\n    //     this.selectedUserLists = [];\r\n    //   } else if (val === 'SPECIFIC_ROLE') {\r\n    //     this.selectedDeptLists = [];\r\n    //     this.selectedUserLists = [];\r\n    //   }\r\n    // },\r\n    filterText(val) {\r\n      this.$refs.tree.filter(val);\r\n    },\r\n  },\r\n  mounted() {\r\n    this.getRoleLists();\r\n    this.getDeptTreeLists();\r\n    this.getUserLists();\r\n    this.getParamDatas();\r\n  },\r\n\r\n  methods: {\r\n    ...mapActions([\"download\"]),\r\n    goExport() {\r\n      this.loading = true;\r\n      this.$confirm(`是否确定导出？`, \"导出\", {\r\n        confirmButtonText: \"确定\",\r\n        cancelButtonText: \"取消\",\r\n        type: \"warning\",\r\n      }).then(() => {\r\n        let s = '';\r\n        Object.keys(this.searchForm).forEach((key, i) => {\r\n          if (this.searchForm[key]) {\r\n            if (i != Object.keys(this.searchForm).length - 1) {\r\n              s = s + key + '=' + this.searchForm[key] + '&';\r\n            } else {\r\n              s = s + key + '=' + this.searchForm[key];\r\n            }\r\n          }\r\n        });\r\n\r\n        this.loading = false;\r\n        let api = '/api/system/role/download';\r\n        let url = `${api}?${s}`;\r\n        let name = `角色管理-${dayjs(new Date()).format('YYYYMMDDHHmmss')}.xlsx`;\r\n        let downData = {\r\n          url: url,\r\n          downLoad: name,\r\n        };\r\n        this.download(downData); // 下载\r\n      })\r\n        .catch(() => {\r\n          this.loading = false;\r\n        });\r\n    },\r\n    getParamDatas() {\r\n      this.$api[\"sysDict/getParam\"]({\r\n        code: \"DATA_PERMISSION_MODULE_NAMES\",\r\n      }).then(data => {\r\n        this.moduleNameLists = data;\r\n      });\r\n    },\r\n    updateDeptRefIds() {\r\n      // 清空之前的 refIds\r\n      this.dataForm.refIds = [];\r\n\r\n      // 从当前选中的部门列表中重新构建 refIds\r\n      this.selectedDeptLists.forEach(item => {\r\n        this.dataForm.refIds.push(item.id);\r\n      });\r\n\r\n      // 记录修改后的 refIds\r\n    },\r\n    getUserData(e) {\r\n      let refIds = [];\r\n      e.forEach(r => {\r\n        refIds.push(r.primaryCode);\r\n      });\r\n      // this.dataForm.refIds = [];\r\n      this.dataForm.allUserDeptIds = refIds;\r\n      this.selectedUserLists = e;\r\n    },\r\n    submitData() {\r\n      this.loading = true;\r\n      if (this.dataForm.permissionType == 8) {\r\n        this.dataForm.permissionTypeCode = this.dataForm.permissionTypeCode1;\r\n\r\n        // 根据不同的权限类型，确保 refIds 是最新的\r\n        if (this.dataForm.permissionTypeCode1 === \"SPECIFIC_DEPT\") {\r\n          // 如果是指定部门，调用 updateDeptRefIds 确保 refIds 是最新的\r\n          this.updateDeptRefIds();\r\n        } else if (this.dataForm.permissionTypeCode1 === \"SPECIFIC_ROLE\") {\r\n          // 如果是指定角色，确保 refIds 是最新的\r\n          this.dataForm.refIds = [];\r\n          this.selectedRoleLists.forEach(item => {\r\n            this.dataForm.refIds.push(item.id);\r\n          });\r\n        } else if (this.dataForm.permissionTypeCode1 === \"SPECIFIC_USER\") {\r\n          // 如果是指定用户，确保 allUserDeptIds 是最新的\r\n          this.dataForm.refIds = [];\r\n          this.selectedUserLists.forEach(item => {\r\n            // 用户ID可能带有 \"user\" 前缀，需要去掉\r\n            const userId = item.primaryCode.startsWith(\"user\")\r\n              ? item.primaryCode.substring(4)\r\n              : item.primaryCode;\r\n            this.dataForm.refIds.push(userId);\r\n          });\r\n        }\r\n      } else {\r\n        this.dataForm.permissionTypeCode = this.dataForm.permissionType;\r\n      }\r\n\r\n      // 打印最终要提交的数据\r\n      this.$api[\"systems/datapermission\"](this.dataForm)\r\n        .then(data => {\r\n          this.$message.success(\"保存成功\");\r\n          this.dataDrawer = false;\r\n          this.loading = false;\r\n        })\r\n        .catch(err => {\r\n          this.loading = false;\r\n          console.error(\"提交数据出错:\", err);\r\n        });\r\n    },\r\n    getAppLists(id) {\r\n      this.$api[\"systems/getAppLists\"]().then(data => {\r\n        this.appLists = data;\r\n        this.$api[\"systems/roleGrantedApps\"]({roleId: id}).then(data => {\r\n          this.appForm.appIds = data;\r\n        });\r\n      });\r\n    },\r\n    saveAppLists(id) {\r\n      this.$api[\"systems/saveRoleApp\"](this.appForm).then(data => {\r\n        this.$message.success(\"应用配置成功\");\r\n        this.appDrawer = false;\r\n      });\r\n    },\r\n    getUserLists() {\r\n      this.$api[\"systems/org-user-tree\"]().then(data => {\r\n        this.userLists = data;\r\n      });\r\n    },\r\n    handleClose(tag) {\r\n      let index = this.selectedRoleLists.findIndex(e => {\r\n        return e.id === tag.id;\r\n      });\r\n      this.selectedRoleLists.splice(index, 1);\r\n      let ids = [];\r\n      this.selectedRoleLists.forEach(element => {\r\n        ids.push(element.id);\r\n      });\r\n\r\n      // 添加安全检查，确保 this.$refs.tree 存在\r\n      if (this.$refs.tree) {\r\n        this.$refs.tree.setCheckedKeys(ids);\r\n      } else {\r\n        console.warn(\"this.$refs.tree is undefined in handleClose\");\r\n      }\r\n\r\n      // 重新设置 dataForm.refIds，确保只包含当前选中的角色 ID\r\n      this.dataForm.refIds = [...ids];\r\n    },\r\n    handleDeptClose(tag) {\r\n      let index = this.selectedDeptLists.findIndex(e => {\r\n        return e.id === tag.id;\r\n      });\r\n      this.selectedDeptLists.splice(index, 1);\r\n      let ids = [];\r\n      this.selectedDeptLists.forEach(element => {\r\n        ids.push(element.id);\r\n      });\r\n\r\n      // 添加安全检查，确保 this.$refs.tree 存在\r\n      if (this.$refs.tree) {\r\n        this.$refs.tree.setCheckedKeys(ids);\r\n      } else {\r\n        console.warn(\"this.$refs.tree is undefined in handleDeptClose\");\r\n      }\r\n\r\n      // 调用 updateDeptRefIds 函数，同步更新 dataForm.refIds\r\n      this.updateDeptRefIds();\r\n    },\r\n    handleUserClose(tag) {\r\n      let index = this.selectedUserLists.findIndex(e => {\r\n        return e.primaryCode === tag.primaryCode;\r\n      });\r\n      this.selectedUserLists.splice(index, 1);\r\n      let ids = [];\r\n      this.selectedUserLists.forEach(element => {\r\n        ids.push(element.primaryCode);\r\n      });\r\n\r\n      // 添加安全检查，确保 this.$refs.ltree 存在\r\n      if (this.$refs.ltree) {\r\n        this.$refs.ltree.setCheckedKeys(ids);\r\n      } else {\r\n        console.warn(\"this.$refs.ltree is undefined in handleUserClose\");\r\n      }\r\n\r\n      // 重新设置 dataForm.allUserDeptIds，确保只包含当前选中的用户 ID\r\n      this.dataForm.allUserDeptIds = [...ids];\r\n    },\r\n    getDeptTreeLists() {\r\n      this.$api[\"systems/organizationTree\"]({parentId: 0}).then(data => {\r\n        this.DeptLists = data;\r\n      });\r\n    },\r\n    getUserTreeLists() {\r\n      this.$api[\"systems/organizationTree\"]({parentId: 0}).then(data => {\r\n        this.DeptLists = data;\r\n      });\r\n    },\r\n    getUserTrees(node, datas) {\r\n      this.dataForm.allUserDeptIds = datas.checkedKeys;\r\n      this.selectedUserLists = datas.checkedNodes;\r\n    },\r\n    getRoleTrees(node, datas) {\r\n      this.dataForm.refIds = datas.checkedKeys;\r\n      this.selectedRoleLists = datas.checkedNodes;\r\n    },\r\n    getDeptTrees(node, datas) {\r\n      // 先清空 selectedDeptLists\r\n      this.selectedDeptLists = datas.checkedNodes;\r\n      // 调用 updateDeptRefIds 函数，同步更新 dataForm.refIds\r\n      this.updateDeptRefIds();\r\n    },\r\n    filterNode(value, data) {\r\n      if (!value) {\r\n        return true;\r\n      }\r\n      return data.roleName.indexOf(value) !== -1;\r\n    },\r\n    filterDeptNode(value, data) {\r\n      if (!value) {\r\n        return true;\r\n      }\r\n      return data.organizationName.indexOf(value) !== -1;\r\n    },\r\n    getRoleLists() {\r\n      this.$api[\"systems/roleList\"]().then(data => {\r\n        this.roleLists = data;\r\n      });\r\n    },\r\n    addRoles() {\r\n      this.dialogRoleVisible = true;\r\n      setTimeout(() => {\r\n        var ids = [];\r\n        this.selectedRoleLists.forEach(e => {\r\n          ids.push(e.id);\r\n        });\r\n        this.$refs.tree.setCheckedKeys(ids);\r\n      }, 100);\r\n    },\r\n    addUsers() {\r\n      this.dialogUserVisible = true;\r\n      setTimeout(() => {\r\n        this.$refs.ltree.setTimer = new Date().getTime();\r\n        var ids = [];\r\n        this.selectedUserLists.forEach(e => {\r\n          ids.push(e.primaryCode);\r\n        });\r\n        this.$refs.ltree.setCheckedKeys(ids);\r\n        this.$refs.ltree.selectedNodes = this.selectedUserLists;\r\n        // console.log(this.$refs.ltree.selectedNode, \"设置\");\r\n      }, 100);\r\n    },\r\n    addDepts() {\r\n      this.dialogDeptVisible = true;\r\n\r\n      setTimeout(() => {\r\n        var ids = [];\r\n        this.selectedDeptLists.forEach(e => {\r\n          ids.push(e.id);\r\n        });\r\n        this.$refs.tree.setCheckedKeys(ids);\r\n      }, 100);\r\n    },\r\n    getRoleUser(e) {\r\n      this.roleDrawer = true;\r\n      setTimeout(() => {\r\n        this.$refs.roleUser.searchForm.roleId = e.id;\r\n        this.$refs.roleUser.searchTableData();\r\n      }, 50);\r\n    },\r\n    getSelectKeys(e) {\r\n      this.roleForm.resourceIds = [];\r\n      e\r\n        && e.forEach(element => {\r\n          this.roleForm.resourceIds.push(element.id + \"\");\r\n        });\r\n    },\r\n    saveMenu() {\r\n      // roleresourcesave\r\n      if (!this.roleForm.resourceIds.length) {\r\n        this.$message.info(\"请选择对应资源在保存\");\r\n      } else {\r\n        this.$api[\"systems/roleresourcesave\"](this.roleForm).then(data => {\r\n          this.$message({\r\n            message: \"保存成功\",\r\n            type: \"success\",\r\n          });\r\n          this.menuDrawer = false;\r\n        });\r\n      }\r\n    },\r\n    getBindResourceIds() {\r\n      this.$api[\"systems/getResourceIds\"]({id: this.roleForm.roleId}).then(\r\n        data => {\r\n          this.resourceIds = [];\r\n          this.roleForm.resourceIds = [];\r\n          data.forEach(e => {\r\n            this.resourceIds.push(Number(e));\r\n            this.roleForm.resourceIds.push(Number(e));\r\n          });\r\n          this.checkStrictly = true;\r\n          this.$nextTick(() => {\r\n            this.$refs.ctree.setTree(this.resourceIds);\r\n            setTimeout(() => {\r\n              this.checkStrictly = false;\r\n            }, 500);\r\n          });\r\n        }\r\n      );\r\n    },\r\n    setDatas(e) {\r\n      this.$api[\"systems/permissionquery\"]({roleId: e.id}).then(data => {\r\n        this.selectModuleName = [];\r\n        this.dataDrawer = true;\r\n        if (data) {\r\n          if (\r\n            data.permissionTypeCode == \"SPECIFIC_DEPT\"\r\n            || data.permissionTypeCode == \"SPECIFIC_USER\"\r\n            || data.permissionTypeCode == \"SPECIFIC_ROLE\"\r\n          ) {\r\n            this.dataForm.permissionType = 8;\r\n            this.dataForm.permissionTypeCode1 = data.permissionTypeCode;\r\n            this.dataForm.permissionTypeCode = data.permissionTypeCode;\r\n            this.dataForm.refIds = [];\r\n            this.selectedDeptLists = [];\r\n            this.selectedRoleLists = [];\r\n            this.selectedUserLists = [];\r\n            data.nameIdList.forEach(e => {\r\n              if (data.permissionTypeCode === \"SPECIFIC_USER\") {\r\n                this.selectedUserLists.push({\r\n                  primaryCode: \"user\" + e.id,\r\n                  label: e.name,\r\n                });\r\n              } else if (data.permissionTypeCode === \"SPECIFIC_DEPT\") {\r\n                this.selectedDeptLists.push({\r\n                  organizationName: e.name,\r\n                  id: e.id,\r\n                });\r\n              } else if (data.permissionTypeCode === \"SPECIFIC_ROLE\") {\r\n                this.selectedRoleLists.push({id: e.id, roleName: e.name});\r\n              }\r\n              this.dataForm.refIds.push(e.id);\r\n            });\r\n          } else {\r\n            this.dataForm.permissionType = data.permissionTypeCode;\r\n            this.dataForm.permissionTypeCode = data.permissionTypeCode;\r\n            this.dataForm.refIds = [];\r\n          }\r\n          this.dataForm.moduleNames = data.moduleNames ? data.moduleNames : [];\r\n        } else {\r\n          this.selectedDeptLists = [];\r\n          this.selectedRoleLists = [];\r\n          this.selectedUserLists = [];\r\n          this.dataForm = {\r\n            moduleNames: [],\r\n            permissionType: \"\",\r\n            permissionTypeCode: \"\",\r\n            permissionTypeCode1: \"\",\r\n            refIds: [],\r\n            roleId: null,\r\n          };\r\n        }\r\n        this.dataForm.roleId = e.id;\r\n      });\r\n    },\r\n    setApps(e) {\r\n      this.appDrawer = true;\r\n      this.appForm.roleId = e.id;\r\n      this.getAppLists(e.id);\r\n    },\r\n    setMenu(e) {\r\n      this.menuDrawer = true;\r\n      this.roleForm.roleId = e.id;\r\n      this.getTrees();\r\n      this.getBindResourceIds();\r\n    },\r\n    handleAdd() {\r\n      this.drawer = true;\r\n      this.drawerName = \"添加\";\r\n      this.ruleForm = _.cloneDeep(defaultForm);\r\n      this.roleStatus = true;\r\n    },\r\n    handleRdit(data) {\r\n      this.ruleForm = JSON.parse(JSON.stringify(data));\r\n      this.roleStatus = !!data.roleStatus;\r\n      this.drawer = true;\r\n      this.drawerName = \"编辑\";\r\n    },\r\n    getParentName(arr, param) {\r\n      let name = \"\";\r\n      arr.forEach(e => {\r\n        if (e.children) {\r\n          if (e.id == param) {\r\n            name = e.roleName;\r\n          }\r\n          this.getParentName(e.children, param);\r\n        } else {\r\n          if (e.id == param) {\r\n            name = e.roleName;\r\n          }\r\n        }\r\n      });\r\n      return name;\r\n    },\r\n    handleDelete(e) {\r\n      this.$confirm(\"您确定要删除该角色吗？\", \"提示\", {\r\n        confirmButtonText: \"确定\",\r\n        cancelButtonText: \"取消\",\r\n        type: \"warning\",\r\n      })\r\n        .then(() => {\r\n          this.$api[\"systems/roleDelete\"]({id: e}).then(data => {\r\n            this.$refs.grid.query();\r\n            this.$message({\r\n              message: \"删除成功\",\r\n              type: \"success\",\r\n            });\r\n          });\r\n        })\r\n        .catch(() => {});\r\n    },\r\n    submitForm(formName) {\r\n      this.$refs[formName].validate(valid => {\r\n        if (valid) {\r\n          this.loading = true;\r\n          this.$api[\"systems/addRole\"](this.ruleForm).then(data => {\r\n            this.drawer = false;\r\n            this.loading = false;\r\n            this.$message({\r\n              type: \"success\",\r\n              message: \"保存成功\",\r\n            });\r\n            this.$refs.grid.query();\r\n          });\r\n        } else {\r\n          return false;\r\n        }\r\n      });\r\n    },\r\n    closeDrawer() {\r\n      this.ruleForm = _.cloneDeep(defaultForm);\r\n      this.$refs.ruleForm.resetFields();\r\n      this.drawer = false;\r\n    },\r\n    resetForm(formName) {\r\n      this.$refs[formName].resetFields();\r\n    },\r\n    searchTable() {\r\n      this.$refs.grid.query();\r\n    },\r\n    resetTable() {\r\n      this.searchForm = _.cloneDeep(defaultSearchForm);\r\n      this.$nextTick(() => {\r\n        this.$refs.grid.query();\r\n      });\r\n    },\r\n    getcolumn(e) {\r\n      this.columns = e;\r\n    },\r\n    getDatas(e) {\r\n      this.tableData = e;\r\n    },\r\n    gerTreeData() {\r\n      if (this.resourceName) {\r\n        this.$api[\"systems/getTreeList\"]({\r\n          resourceName: this.resourceName,\r\n        }).then(data => {\r\n          this.treedata = data;\r\n        });\r\n      } else {\r\n        this.searchForm.parentId = null;\r\n        this.getTrees();\r\n      }\r\n    },\r\n    getTrees() {\r\n      this.$api[\"systems/getPremissTree\"]({parentId: 0}).then(data => {\r\n        this.treedata = data;\r\n      });\r\n    },\r\n  },\r\n};\r\n</script>\r\n<style lang=\"less\" scoped>\r\n.demo-drawer-footer {\r\n  width: 100%;\r\n  position: absolute;\r\n  bottom: 0;\r\n  left: 0;\r\n  border-top: 1px solid #e8e8e8;\r\n  padding: 10px 16px;\r\n  text-align: right;\r\n  background: #fff;\r\n  z-index: 100;\r\n}\r\n.el-dropdown-link {\r\n  cursor: pointer;\r\n  color: #409eff;\r\n}\r\n.el-icon-arrow-down {\r\n  font-size: 12px;\r\n}\r\n.appCard {\r\n  width: 100%;\r\n  overflow: hidden;\r\n  height: 60px;\r\n  margin-bottom: 10px;\r\n  position: relative;\r\n  border: 1px solid #ccc;\r\n  border-radius: 5px;\r\n}\r\n.cardTitle {\r\n  position: absolute;\r\n  z-index: 999;\r\n  width: 150px;\r\n  text-align: center;\r\n  font-weight: bold;\r\n  height: 60px;\r\n  line-height: 60px;\r\n  top: 0px;\r\n  left: 0;\r\n}\r\n</style>\r\n"]}]}