{"remainingRequest": "D:\\bw\\idcardbox-vue\\bysc-vue-system\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\bw\\idcardbox-vue\\bysc-vue-system\\src\\bysc_system\\views\\terminalAssignment\\index.vue?vue&type=template&id=10f0b2ce&scoped=true", "dependencies": [{"path": "D:\\bw\\idcardbox-vue\\bysc-vue-system\\src\\bysc_system\\views\\terminalAssignment\\index.vue", "mtime": 1753953805612}, {"path": "D:\\bw\\idcardbox-vue\\bysc-vue-system\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745221302709}, {"path": "D:\\bw\\idcardbox-vue\\bysc-vue-system\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745221302709}, {"path": "D:\\bw\\idcardbox-vue\\bysc-vue-system\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1745221307620}, {"path": "D:\\bw\\idcardbox-vue\\bysc-vue-system\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 1745221315417}, {"path": "D:\\bw\\idcardbox-vue\\bysc-vue-system\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745221302709}, {"path": "D:\\bw\\idcardbox-vue\\bysc-vue-system\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1745221307761}], "contextDependencies": [], "result": ["\n<div>\n  <el-row>\n    <el-col :span=\"24\">\n      <Grid\n        api=\"terminal/terminal-page\"\n        :event-bus=\"searchEventBus\"\n        :search-params=\"searchForm\"\n        :newcolumn=\"columns\"\n        @datas=\"getDatas\"\n        @columnChange=\"getColumn\"\n        :auto-load=\"true\"\n        ref=\"grid\"\n      >\n        <div slot=\"search\">\n          <el-form\n            :inline=\"true\"\n            :model=\"searchForm\"\n            class=\"demo-form-inline\"\n          >\n\n            <el-form-item label=\"IP地址\">\n              <el-input\n                style=\"width: 200px;margin:0 10px 0 0;\"\n                v-model.trim=\"searchForm.ipAddress\"\n                size=\"small\"\n                placeholder=\"请输入IP地址\"\n              ></el-input>\n            </el-form-item>\n            <el-form-item label=\"证件柜名称\">\n              <el-input\n                style=\"width: 200px;margin:0 10px 0 0;\"\n                v-model.trim=\"searchForm.icbName\"\n                size=\"small\"\n                placeholder=\"请输入证件柜名称\"\n              ></el-input>\n            </el-form-item>\n            <el-form-item label=\"状态\">\n              <el-select\n                size=\"small\"\n                clearable\n                @keydown.enter.native.prevent=\"searchTable\"\n                v-model=\"searchForm.status\"\n                placeholder=\"请选择状态\"\n              >\n                <el-option label=\"在线\" :value=\"1\"></el-option>\n                <el-option label=\"离线\" :value=\"0\"></el-option>\n              </el-select>\n            </el-form-item>\n            <el-form-item label=\"分配状态\">\n              <el-select\n                size=\"small\"\n                clearable\n                @keydown.enter.native.prevent=\"searchTable\"\n                v-model=\"searchForm.distributeStatus\"\n                placeholder=\"请选择分配状态\"\n              >\n                <el-option label=\"已分配\" value=\"1\"></el-option>\n                <el-option label=\"待分配\" value=\"0\"></el-option>\n                 <el-option label=\"分配中\" value=\"2\"></el-option>\n              </el-select>\n            </el-form-item>\n            <el-form-item>\n              <el-button\n                size=\"small\"\n                type=\"primary\"\n                style=\"margin: 0 0 0 10px\"\n                @click=\"searchTable\"\n                >搜索</el-button>\n              <el-button size=\"small\" @click=\"resetTable\">重置</el-button>\n            </el-form-item>\n          </el-form>\n        </div>\n        <el-table slot=\"table\" slot-scope=\"{}\" v-loading=\"tableLoading\" ref=\"multipleTable\" :data=\"tableData\" stripe style=\"width: 100%\">\n          <el-table-column fixed=\"left\" :align=\"'center'\" label=\"序号\" type=\"index\" width=\"50\">\n          </el-table-column>\n          <template v-for=\"(item, index) in columns\">\n            <el-table-column\n              v-if=\"item.slot === 'status'\"\n              :show-overflow-tooltip=\"true\"\n              :align=\"item.align ? item.align : 'center'\"\n              :key=\"index\"\n              :prop=\"item.key\"\n              :label=\"item.title\"\n              min-width=\"100\"\n            >\n              <template slot-scope=\"scope\">\n                <el-tag :type=\"scope.row.status === '1' ? 'success' : 'danger'\">\n                  {{ scope.row.status == '1' ? '在线' : '离线' }}\n                </el-tag>\n              </template>\n            </el-table-column>\n            <el-table-column\n              v-else-if=\"item.slot === 'distributeStatus'\"\n              :show-overflow-tooltip=\"true\"\n              :align=\"item.align ? item.align : 'center'\"\n              :key=\"index\"\n              :prop=\"item.key\"\n              :label=\"item.title\"\n              min-width=\"100\"\n            >\n              <template slot-scope=\"scope\">\n                <el-tag :type=\"scope.row.distributeStatus === 'assigned' ? 'success' : 'warning'\">\n                  {{ scope.row.distributeStatus === '1' ? '已分配' : '待分配' }}\n                </el-tag>\n              </template>\n            </el-table-column>\n            <el-table-column\n              v-else\n              :show-overflow-tooltip=\"true\"\n              :key=\"item.key\"\n              :prop=\"item.key\"\n              :label=\"item.title\"\n              :min-width=\"item.width ? item.width : '150'\"\n              :align=\"item.align ? item.align : 'center'\"\n            >\n            </el-table-column>\n          </template>\n          <el-table-column\n            fixed=\"right\"\n            align=\"center\"\n            label=\"操作\"\n            type=\"action\"\n            width=\"200\"\n          >\n            <template slot-scope=\"scope\">\n              <el-button\n                v-if=\"scope.row.distributeStatus === '1'\"\n                style=\"margin-right:6px\"\n                @click=\"handleChangeTenant(scope.row)\"\n                type=\"text\"\n                size=\"small\"\n                v-permission=\"'terminal_admin_change'\"\n                >更改租户</el-button>\n              <el-button\n                v-if=\"scope.row.distributeStatus === '0'\"\n                style=\"margin-right:6px\"\n                @click=\"handleAssignTenant(scope.row)\"\n                type=\"text\"\n                size=\"small\"\n                v-permission=\"'terminal_admin_assign'\"\n                >分配租户</el-button>\n            </template>\n          </el-table-column>\n        </el-table>\n      </Grid>\n    </el-col>\n  </el-row>\n\n  <!-- 分配租户弹窗 -->\n  <AssignTenantDialog\n    :visible.sync=\"assignDialogVisible\"\n    :terminal-info=\"currentTerminal\"\n    :tenant-list=\"tenantList\"\n    :loading=\"submitLoading\"\n    @confirm=\"handleAssignConfirm\"\n    @close=\"handleAssignClose\"\n  />\n\n  <!-- 更改租户弹窗 -->\n  <ChangeTenantDialog\n    :visible.sync=\"changeDialogVisible\"\n    :terminal-info=\"currentTerminal\"\n    :tenant-list=\"tenantList\"\n    :loading=\"submitLoading\"\n    @confirm=\"handleChangeConfirm\"\n    @close=\"handleChangeClose\"\n  />\n</div>\n", null]}