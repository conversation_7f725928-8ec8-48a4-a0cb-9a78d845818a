{"remainingRequest": "D:\\bw\\idcardbox-vue\\bysc-vue-system\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\bw\\idcardbox-vue\\bysc-vue-system\\src\\bysc_system\\views\\role\\index.vue?vue&type=template&id=f7dbd3ca&scoped=true", "dependencies": [{"path": "D:\\bw\\idcardbox-vue\\bysc-vue-system\\src\\bysc_system\\views\\role\\index.vue", "mtime": 1753952638725}, {"path": "D:\\bw\\idcardbox-vue\\bysc-vue-system\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745221302709}, {"path": "D:\\bw\\idcardbox-vue\\bysc-vue-system\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745221302709}, {"path": "D:\\bw\\idcardbox-vue\\bysc-vue-system\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1745221307620}, {"path": "D:\\bw\\idcardbox-vue\\bysc-vue-system\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 1745221315417}, {"path": "D:\\bw\\idcardbox-vue\\bysc-vue-system\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745221302709}, {"path": "D:\\bw\\idcardbox-vue\\bysc-vue-system\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1745221307761}], "contextDependencies": [], "result": ["\n<div>\n  <el-row>\n    <el-col :span=\"24\">\n      <Grid\n        api=\"systems/rolePage\"\n        :event-bus=\"searchEventBus\"\n        :search-params=\"searchForm\"\n        :newcolumn=\"columns\"\n        @datas=\"getDatas\"\n        @columnChange=\"getcolumn\"\n        ref=\"grid\"\n      >\n        <div slot=\"search\">\n          <el-input\n            style=\"width: 200px; margin: 0 10px 0 0\"\n            v-model.trim=\"searchForm.name\"\n            size=\"small\"\n            placeholder=\"请输入名称\"\n          ></el-input>\n          <el-input\n            style=\"width: 200px; margin: 0 10px 0 0\"\n            v-model.trim=\"searchForm.code\"\n            size=\"small\"\n            placeholder=\"请输入标识\"\n          ></el-input>\n          <el-button\n            size=\"small\"\n            type=\"primary\"\n            style=\"margin: 0 0 0 10px\"\n            @click=\"searchTable\"\n            >搜索</el-button\n          >\n          <el-button size=\"small\" @click=\"resetTable\">重置</el-button>\n        </div>\n        <div slot=\"action\">\n          <el-button size=\"small\" type=\"primary\" @click=\"handleAdd\"\n            >添加</el-button\n          >\n          <el-button size=\"small\" type=\"primary\" @click=\"goExport\"\n            >导出</el-button\n          >\n        </div>\n        <el-table\n          slot=\"table\"\n          slot-scope=\"{ loading }\"\n          v-loading=\"loading\"\n          :data=\"tableData\"\n          stripe\n          style=\"width: 100%\"\n        >\n          <el-table-column\n            fixed=\"left\"\n            :align=\"'center'\"\n            type=\"selection\"\n            width=\"55\"\n          >\n          </el-table-column>\n          <el-table-column\n            fixed=\"left\"\n            :align=\"'center'\"\n            label=\"序号\"\n            type=\"index\"\n            width=\"50\"\n          >\n          </el-table-column>\n          <template v-for=\"(item, index) in columns\">\n            <el-table-column\n              v-if=\"item.slot === 'roleStatus'\"\n              :show-overflow-tooltip=\"true\"\n              :align=\"item.align ? item.align : 'center'\"\n              :key=\"index\"\n              :prop=\"item.key\"\n              :label=\"item.title\"\n              min-width=\"180\"\n            >\n              <template slot-scope=\"scope\">\n                {{ scope.row[item.slot] ? \"启用\" : \"禁用\" }}\n              </template>\n            </el-table-column>\n            <el-table-column\n              v-else-if=\"item.slot === 'roleType'\"\n              :show-overflow-tooltip=\"true\"\n              :align=\"item.align ? item.align : 'center'\"\n              :key=\"index\"\n              :prop=\"item.key\"\n              :label=\"item.title\"\n              min-width=\"180\"\n            >\n              <template slot-scope=\"scope\">\n                {{ scope.row[item.slot] === 1 ? '超管' :\n                   scope.row[item.slot] === 2 ? '管理员' :\n                   scope.row[item.slot] === 3 ? '普通用户' : '' }}\n              </template>\n            </el-table-column>\n            <el-table-column\n              v-else\n              :show-overflow-tooltip=\"true\"\n              :key=\"item.key\"\n              :prop=\"item.key\"\n              :label=\"item.title\"\n              :min-width=\"item.width ? item.width : '150'\"\n              :align=\"item.align ? item.align : 'center'\"\n            >\n            </el-table-column>\n          </template>\n          <el-table-column\n            fixed=\"right\"\n            align=\"center\"\n            label=\"操作\"\n            type=\"action\"\n            width=\"180\"\n          >\n            <template slot-scope=\"scope\">\n              <el-button @click=\"getRoleUser(scope.row)\" type=\"text\"\n                >角色用户</el-button\n              >\n              <el-dropdown style=\"margin-left: 6px\">\n                <span class=\"el-dropdown-link\" style=\"font-size: 14px\">\n                  更多<i class=\"el-icon-arrow-down el-icon--right\"></i>\n                </span>\n                <el-dropdown-menu slot=\"dropdown\">\n                  <el-dropdown-item @click.native=\"handleRdit(scope.row)\">\n                    <el-button type=\"text\">编辑</el-button>\n                  </el-dropdown-item>\n                  <el-dropdown-item @click.native=\"setMenu(scope.row)\">\n                    <el-button type=\"text\">资源配置</el-button>\n                  </el-dropdown-item>\n                  <el-dropdown-item @click.native=\"setDatas(scope.row)\">\n                    <el-button type=\"text\">数据配置</el-button>\n                  </el-dropdown-item>\n                  <el-dropdown-item @click.native=\"setApps(scope.row)\">\n                    <el-button type=\"text\">应用配置</el-button>\n                  </el-dropdown-item>\n                  <el-dropdown-item\n                    v-if=\"scope.row.canDelete\"\n                    @click.native=\"handleDelete(scope.row.id)\"\n                  >\n                    <el-button type=\"text\">删除</el-button>\n                  </el-dropdown-item>\n                </el-dropdown-menu>\n              </el-dropdown>\n            </template>\n          </el-table-column>\n        </el-table>\n      </Grid>\n    </el-col>\n  </el-row>\n  <el-drawer\n    size=\"50%\"\n    title=\"资源配置\"\n    :visible.sync=\"menuDrawer\"\n    :direction=\"direction\"\n  >\n    <div style=\"width: 100%; padding: 0 10px; margin-bottom: 100px\">\n      <checkTree\n        ref=\"ctree\"\n        :checkStrictly=\"checkStrictly\"\n        :defaultexpandall=\"true\"\n        :tree-props=\"treeProps\"\n        :tree-data=\"treedata\"\n        @treeNode=\"getSelectKeys\"\n      ></checkTree>\n      <div class=\"demo-drawer-footer\">\n        <el-button\n          size=\"small\"\n          type=\"primary\"\n          @click=\"saveMenu()\"\n          :loading=\"loading\"\n          >保存</el-button\n        >\n      </div>\n    </div>\n  </el-drawer>\n  <el-drawer\n    size=\"50%\"\n    :title=\"drawerName\"\n    :visible.sync=\"drawer\"\n    :direction=\"direction\"\n  >\n    <div style=\"width: 100%; padding: 0 10px\">\n      <el-form\n        :model=\"ruleForm\"\n        :rules=\"rules\"\n        ref=\"ruleForm\"\n        label-width=\"100px\"\n        class=\"demo-ruleForm\"\n      >\n        <el-form-item label=\"名称\" prop=\"roleName\">\n          <el-input\n            size=\"small\"\n            maxlength=\"15\"\n            v-model.trim=\"ruleForm.roleName\"\n          ></el-input>\n        </el-form-item>\n        <el-form-item label=\"标识\" prop=\"roleKey\">\n          <el-input\n            size=\"small\"\n            maxlength=\"32\"\n            v-model.trim=\"ruleForm.roleKey\"\n          ></el-input>\n        </el-form-item>\n\n        <el-form-item label=\"角色类型\" prop=\"roleType\">\n          <el-select\n            v-model=\"ruleForm.roleType\"\n            size=\"small\"\n            placeholder=\"请选择角色类型\"\n          >\n            <el-option label=\"超管\" :value=\"1\"></el-option>\n            <el-option label=\"管理员\" :value=\"2\"></el-option>\n            <el-option label=\"普通用户\" :value=\"3\"></el-option>\n          </el-select>\n        </el-form-item>\n\n        <el-form-item label=\"角色描述\" prop=\"comments\">\n          <el-input\n            type=\"textarea\"\n            size=\"small\"\n            maxlength=\"200\"\n            v-model.trim=\"ruleForm.comments\"\n          ></el-input>\n        </el-form-item>\n        <el-form-item label=\"是否启用\" prop=\"roleStatus\">\n          <el-switch v-model.trim=\"roleStatus\"></el-switch>\n        </el-form-item>\n        <div class=\"demo-drawer-footer\">\n          <el-button size=\"small\" @click=\"closeDrawer\">关闭</el-button>\n          <el-button\n            size=\"small\"\n            type=\"primary\"\n            @click=\"submitForm('ruleForm')\"\n            >保存</el-button\n          >\n        </div>\n      </el-form>\n    </div>\n  </el-drawer>\n  <el-drawer\n    size=\"700px\"\n    stitle=\"角色用户\"\n    :visible.sync=\"roleDrawer\"\n    :direction=\"direction\"\n  >\n    <div style=\"width: 100%; padding: 0 10px\">\n      <roleUser ref=\"roleUser\"></roleUser>\n    </div>\n  </el-drawer>\n  <el-drawer\n    size=\"700px\"\n    title=\"数据配置\"\n    :modal=\"false\"\n    :visible.sync=\"dataDrawer\"\n    :direction=\"direction\"\n  >\n    <div style=\"width: 100%; padding: 0 10px; position: relative\">\n      <!-- <span style=\"font-weight:bold\">数据配置：</span> -->\n      <el-radio-group v-model=\"dataForm.permissionType\">\n        <el-radio style=\"width: 100%; margin-top: 5px\" :label=\"'ME'\"\n          >本人</el-radio\n        >\n        <!-- <el-radio style=\"width: 100%; margin-top: 5px\" :label=\"'SUBORDINATE'\"\n          >本人及下属</el-radio\n        > -->\n        <!-- <el-radio\n          style=\"width: 100%; margin-top: 5px\"\n          :label=\"'MY_DEPARTMENT'\"\n          >本部门</el-radio\n        > -->\n        <!-- <el-radio\n          style=\"width: 100%; margin-top: 5px\"\n          :label=\"'MY_DEPARTMENT_AND_SUB'\"\n          >本部门及下属</el-radio\n        > -->\n        <el-radio style=\"width: 100%; margin-top: 5px\" :label=\"'ALL'\"\n          >全部</el-radio\n        >\n        <el-radio style=\"width: 100%; margin-top: 5px\" :label=\"8\"\n          >指定范围</el-radio\n        >\n      </el-radio-group>\n      <div style=\"margin-top: 10px\" v-show=\"dataForm.permissionType === 8\">\n        <span>指定范围：</span>\n        <el-radio-group v-model=\"dataForm.permissionTypeCode1\">\n          <el-radio :label=\"'SPECIFIC_USER'\">指定用户</el-radio>\n          <el-radio :label=\"'SPECIFIC_DEPT'\">指定部门/分子公司</el-radio>\n          <el-radio :label=\"'SPECIFIC_ROLE'\">指定组织角色</el-radio>\n        </el-radio-group>\n      </div>\n      <div\n        v-show=\"dataForm.permissionType === 8\"\n        style=\"width: 100%; height: 1px; background: #ccc; margin-top: 15px\"\n      ></div>\n      <div v-show=\"dataForm.permissionType === 8\" style=\"margin-top: 15px\">\n        <div\n          style=\"margin-top: 5px\"\n          v-show=\"dataForm.permissionTypeCode1 == 'SPECIFIC_USER'\"\n        >\n          <div style=\"font-weight: bold; margin-bottom: 8px\">指定用户</div>\n          <el-button\n            style=\"margin-right: 5px\"\n            @click=\"addUsers\"\n            type=\"primary\"\n            size=\"small\"\n            icon=\"el-icon-circle-plus-outline\"\n            >添加</el-button\n          >\n          <el-tag\n            style=\"margin: 5px\"\n            v-for=\"tag in selectedUserLists\"\n            :key=\"tag.id\"\n            closable\n            @close=\"handleUserClose(tag)\"\n          >\n            {{ tag.label }}\n          </el-tag>\n        </div>\n        <div\n          style=\"margin-top: 5px\"\n          v-show=\"dataForm.permissionTypeCode1 == 'SPECIFIC_DEPT'\"\n        >\n          <div style=\"font-weight: bold; margin-bottom: 8px\">指定部门</div>\n          <el-button\n            style=\"margin-right: 5px\"\n            @click=\"addDepts\"\n            type=\"primary\"\n            size=\"small\"\n            icon=\"el-icon-circle-plus-outline\"\n            >添加</el-button\n          >\n          <el-tag\n            style=\"margin: 5px\"\n            v-for=\"tag in selectedDeptLists\"\n            :key=\"tag.id\"\n            closable\n            @close=\"handleDeptClose(tag)\"\n          >\n            {{ tag.organizationName }}\n          </el-tag>\n        </div>\n        <div\n          style=\"margin-top: 5px\"\n          v-show=\"dataForm.permissionTypeCode1 == 'SPECIFIC_ROLE'\"\n        >\n          <div style=\"font-weight: bold; margin-bottom: 8px\">\n            指定组织角色\n          </div>\n          <el-button\n            style=\"margin-right: 5px\"\n            @click=\"addRoles\"\n            type=\"primary\"\n            size=\"small\"\n            icon=\"el-icon-circle-plus-outline\"\n            >添加</el-button\n          >\n          <el-tag\n            style=\"margin: 5px\"\n            v-for=\"tag in selectedRoleLists\"\n            :key=\"tag.id\"\n            closable\n            @close=\"handleClose(tag)\"\n          >\n            {{ tag.roleName }}\n          </el-tag>\n        </div>\n        <el-dialog\n          title=\"指定用户\"\n          :modal=\"false\"\n          :visible.sync=\"dialogUserVisible\"\n          width=\"50%\"\n        >\n          <div style=\"width: 100%; height: 500px; overflow: auto\">\n            <!-- <div style=\"width:100%\">\n              <el-input\n                placeholder=\"输入关键字进行过滤\"\n                size=\"small\"\n                style=\"margin-bottom:10px;width:48%\"\n                v-model=\"filterText\">\n              </el-input>\n            </div> -->\n            <div\n              style=\"\n                height: 100%;\n                width: 50%;\n                float: left;\n                padding: 10px;\n                overflow-y: scroll;\n              \"\n            >\n              <Lazytrees ref=\"ltree\" @treeNode=\"getUserData\"></Lazytrees>\n              <!-- <el-tree\n                :data=\"userLists\"\n                show-checkbox\n                :check-strictly=\"true\"\n                @check=\"getUserTrees\"\n                node-key=\"primaryCode\"\n                ref=\"tree\"\n                :default-expand-all=\"true\"\n                :filter-node-method=\"filterNode\"\n                :props=\"defaultUserProps\">\n              </el-tree> -->\n            </div>\n            <div\n              style=\"\n                height: 100%;\n                width: 50%;\n                float: left;\n                padding: 10px;\n                border: 1px solid #f2f2f2;\n              \"\n            >\n              <div\n                style=\"\n                  text-align: center;\n                  height: 30px;\n                  width: 100%;\n                  line-height: 30px;\n                \"\n              >\n                已选择的用户\n              </div>\n              <div>\n                <el-tag\n                  style=\"margin: 5px\"\n                  v-for=\"tag in selectedUserLists\"\n                  :key=\"tag.primaryCode\"\n                  closable\n                  @close=\"handleUserClose(tag)\"\n                >\n                  {{ tag.label }}\n                </el-tag>\n              </div>\n            </div>\n          </div>\n          <span slot=\"footer\" class=\"dialog-footer\">\n            <el-button\n              size=\"small\"\n              type=\"primary\"\n              @click=\"dialogUserVisible = false\"\n              >确 定</el-button\n            >\n          </span>\n        </el-dialog>\n        <el-dialog\n          title=\"选择部门\"\n          :modal=\"false\"\n          :visible.sync=\"dialogDeptVisible\"\n          width=\"50%\"\n        >\n          <div style=\"width: 100%; height: 500px; overflow: auto\">\n            <div style=\"height: 100%; width: 50%; float: left; padding: 10px\">\n              <el-input\n                placeholder=\"输入关键字进行过滤\"\n                size=\"small\"\n                style=\"margin-bottom: 10px; width: 90%\"\n                v-model=\"filterText\"\n              >\n              </el-input>\n              <el-tree\n                :data=\"DeptLists\"\n                show-checkbox\n                :check-strictly=\"false\"\n                @check=\"getDeptTrees\"\n                node-key=\"id\"\n                ref=\"tree\"\n                :filter-node-method=\"filterDeptNode\"\n                :props=\"defaultDeptProps\"\n              >\n              </el-tree>\n            </div>\n            <div\n              style=\"\n                height: 100%;\n                width: 50%;\n                float: left;\n                padding: 10px;\n                border: 1px solid #f2f2f2;\n              \"\n            >\n              <div\n                style=\"\n                  text-align: center;\n                  height: 30px;\n                  width: 100%;\n                  line-height: 30px;\n                \"\n              >\n                已选择的部门\n              </div>\n              <div>\n                <el-tag\n                  style=\"margin: 5px\"\n                  v-for=\"tag in selectedDeptLists\"\n                  :key=\"tag.id\"\n                  closable\n                  @close=\"handleDeptClose(tag)\"\n                >\n                  {{ tag.organizationName }}\n                </el-tag>\n              </div>\n            </div>\n          </div>\n          <span slot=\"footer\" class=\"dialog-footer\">\n            <el-button\n              size=\"small\"\n              type=\"primary\"\n              @click=\"\n                dialogDeptVisible = false;\n                updateDeptRefIds();\n              \"\n              >确 定</el-button\n            >\n          </span>\n        </el-dialog>\n        <el-dialog\n          title=\"组织选择角色\"\n          :modal=\"false\"\n          :visible.sync=\"dialogRoleVisible\"\n          width=\"50%\"\n        >\n          <div style=\"width: 100%; height: 500px; overflow: auto\">\n            <div style=\"height: 100%; width: 50%; float: left; padding: 10px\">\n              <el-input\n                placeholder=\"输入关键字进行过滤\"\n                size=\"small\"\n                style=\"margin-bottom: 10px; width: 90%\"\n                v-model=\"filterText\"\n              >\n              </el-input>\n              <el-tree\n                :data=\"roleLists\"\n                show-checkbox\n                @check=\"getRoleTrees\"\n                node-key=\"id\"\n                ref=\"tree\"\n                :filter-node-method=\"filterNode\"\n                :props=\"defaultProps\"\n              >\n              </el-tree>\n            </div>\n            <div\n              style=\"\n                height: 100%;\n                width: 50%;\n                float: left;\n                padding: 10px;\n                border: 1px solid #f2f2f2;\n              \"\n            >\n              <div\n                style=\"\n                  text-align: center;\n                  height: 30px;\n                  width: 100%;\n                  line-height: 30px;\n                \"\n              >\n                已选择的组织角色\n              </div>\n              <div>\n                <el-tag\n                  style=\"margin: 5px\"\n                  v-for=\"tag in selectedRoleLists\"\n                  :key=\"tag.id\"\n                  closable\n                  @close=\"handleClose(tag)\"\n                >\n                  {{ tag.roleName }}\n                </el-tag>\n              </div>\n            </div>\n          </div>\n          <span slot=\"footer\" class=\"dialog-footer\">\n            <el-button\n              size=\"small\"\n              type=\"primary\"\n              @click=\"dialogRoleVisible = false\"\n              >确 定</el-button\n            >\n          </span>\n        </el-dialog>\n      </div>\n      <div style=\"padding-top: 20px\">\n        <el-divider content-position=\"left\">生效模块</el-divider>\n        <el-checkbox-group v-model=\"dataForm.moduleNames\" size=\"small\">\n          <div\n            style=\"margin-top: 10px\"\n            v-for=\"item in moduleNameLists\"\n            :key=\"item.id\"\n          >\n            <el-checkbox :label=\"item.dictCode\" border>{{\n              item.dictName\n            }}</el-checkbox>\n          </div>\n        </el-checkbox-group>\n      </div>\n\n      <div style=\"width: 100%; position: absolute; right: 20px; top: 85vh\">\n        <el-button\n          style=\"float: right\"\n          size=\"small\"\n          type=\"primary\"\n          @click=\"submitData\"\n          :loading=\"loading\"\n          >{{ loading ? \"提交中 ...\" : \"确 定\" }}</el-button\n        >\n      </div>\n    </div>\n  </el-drawer>\n  <!-- 应用配置 -->\n  <el-drawer\n    size=\"700px\"\n    title=\"应用配置\"\n    :visible.sync=\"appDrawer\"\n    :direction=\"direction\"\n  >\n    <div style=\"width: 100%; padding: 0 10px\">\n      <el-checkbox-group v-model=\"appForm.appIds\">\n        <el-row :gutter=\"20\">\n          <el-col\n            :span=\"6\"\n            v-for=\"(item, index) in appLists\"\n            :key=\"index + 'app'\"\n          >\n            <div class=\"appCard\">\n              <el-checkbox\n                style=\"position: absolute; left: 3px\"\n                :key=\"item.appId\"\n                :label=\"item.appId\"\n              >\n                <div class=\"cardTitle\">{{ item.appName }}</div>\n              </el-checkbox>\n            </div>\n          </el-col>\n        </el-row>\n      </el-checkbox-group>\n      <div>\n        <el-button\n          style=\"float: right; margin-top: 60px\"\n          @click=\"saveAppLists()\"\n          type=\"primary\"\n          size=\"small\"\n        >\n          保存\n        </el-button>\n      </div>\n    </div>\n  </el-drawer>\n</div>\n", null]}